import json
import asyncio
from typing import Dict, Any, List, Optional
from pydantic import BaseModel
from src.models.aava_models import DAResponse, DAServerError, DAValueError
from src.services.database.project_service import ProjectService
from src.utils.api_helper.api_client import APIClient
from src.config.settings import settings
from src.config.pipeline_config import AGENT_NAMES
from src.utils.logger import AppLogger
from src.utils.helpers.text_parser import get_content_inside_markdown
from src.models.brainstormer_models import (
    MarketResearch,
    LeanBusinessCanvas,
    UserPersona,
    SwotAnalysis,
    FeaturesByCategory,
)

logger = AppLogger(__name__).get_logger()


class ConversationMessage(BaseModel):
    content: str
    role: str  # "system", "user", "assistant"


class ConversationalAgentPayload(BaseModel):
    mode: str
    useCaseIdentifier: str
    userSignature: str
    conversations: List[ConversationMessage]


class AAVAAIService:
    def __init__(self, base_url: str, project_service: Optional[ProjectService] = None):
        self.base_url = base_url
        self.project_service = project_service
        # Add access-key header for API calls
        headers = {"access-key": settings.access_key} if settings.access_key else {}
        self.api_client = APIClient(base_url=base_url, headers=headers)

    async def make_api_call(
        self, endpoint: str, payload: Dict[str, Any]
    ) -> Dict[str, Any]:
        return await self.api_client.post(endpoint, data=payload)

    async def _load_step_from_db(self, run_id: str, step_name: str) -> Dict[str, Any]:
        """Load step data from database as fallback"""
        if not hasattr(self, "project_service") or not self.project_service:
            return None

        try:
            # Get project by run_id
            logger.warning(f"Getting proj from runid:  {run_id}")
            project = await self.project_service.get_project_by_run_id(run_id)
            if not project:
                logger.warning(f"No project found for run_id: {run_id}")
                return None

            logger.warning(f"Got proj from runid:  {project}")

            project_id = project["id"]
            logger.debug(f"Found project_id: {project_id} for run_id: {run_id}")

            if step_name == "market_research":
                data = await self.project_service.get_market_research(project_id)
            elif step_name == "lbc":
                data = await self.project_service.get_lean_business_canvas(project_id)
            elif step_name == "persona":
                data = await self.project_service.get_user_personas(project_id)
            elif step_name == "swot":
                data = await self.project_service.get_swot_analysis(project_id)
            elif step_name == "features":
                data = await self.project_service.get_features(project_id)
            elif step_name == "roadmap":
                data = await self.project_service.get_roadmap_tasks(project_id)
            else:
                logger.warning(f"Unknown step name: {step_name}")
                return None

            return data
        except Exception as e:
            logger.error(f"Failed to load step '{step_name}' from database: {e}")
            return None

    def _get_json_diff(
        self, original: Dict[str, Any], updated: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Compares two dictionaries and returns a dict with only the changed values."""
        diff = {}
        all_keys = set(original.keys()) | set(updated.keys())
        for key in all_keys:
            if original.get(key) != updated.get(key):
                diff[key] = updated.get(key)
        return diff


class PipelineAgentService(AAVAAIService):
    def __init__(self, base_url: str, project_service: Optional[ProjectService] = None):
        super().__init__(base_url)
        self.project_service = project_service  # Will be injected

    def set_project_service(self, project_service: ProjectService):
        """Inject project service dependency"""
        if project_service is None:
            raise ValueError("Project service cannot be None")
        self.project_service = project_service

    async def generate_market_research(
        self, step_name: str, context: Dict[str, Any], user_signature: str
    ):
        try:
            user_request = context.get("user_request", "")
            if user_request:
                # Perform web search to get market data
                response = await self.perform_web_search(
                    user_request, user_signature, usecase="market_research"
                )
                response_text = response.get("content", "")
                logger.debug(f"Web search response_text: {response_text[:200]}...")
                return self._parse_response_by_step(step_name, response_text)

        except Exception as e:
            logger.warning(
                f"Web search failed for market research: {e}. Proceeding without search data."
            )

    async def generate_artifact(
        self, step_name: str, context: Dict[str, Any], user_signature: str
    ) -> Dict[str, Any]:
        """Calls the pipeline agent to generate a full artifact."""
        agent_config = AGENT_NAMES.get(step_name)
        if not agent_config:
            raise ValueError(f"No agent configuration found for step: {step_name}")

            # Format the prompt with context
        try:
            formatted_prompt = agent_config["prompt"].format(**context)
        except KeyError as e:
            raise ValueError(f"Missing required context key for prompt formatting: {e}")

        payload = {
            "mode": agent_config["name"],
            "promptOverride": False,
            "useCaseIdentifier": f"{agent_config["name"]}@ASCENDION@EXPERIENCE_ENGINEERING@MICHELANGELO@CAPEX",
            "userSignature": user_signature,
            "image": None,
            "prompt": formatted_prompt,
        }

        response = await self.make_api_call(
            settings.individual_agent_service_endpoint, payload
        )

        logger.debug(f"Raw response: {response}")
        response = DAResponse(**response)
        choices = response.response.choices
        if not choices:
            logger.error("No choices found in the DA response")
            raise DAServerError("No choices found in the DA response")

        response_text = choices[0].text

        if not response_text:
            logger.error("No text found in the choice")
            raise DAValueError("No text found in the choice")

        # Parse and validate the response
        return self._parse_response_by_step(step_name, response_text)

    def _parse_response_by_step(
        self, step_name: str, response_text: str
    ) -> Dict[str, Any]:
        """Parse response text based on step name and return validated dict"""
        try:
            # Check if response_text is empty or None
            if not response_text or not response_text.strip():
                logger.error(f"Empty response text for step {step_name}")
                raise DAValueError(f"Empty response received for step {step_name}")

            logger.debug(f"Raw response_text for {step_name}: {response_text[:500]}...")

            # Extract JSON from markdown if present
            try:
                json_content = get_content_inside_markdown(response_text, "json")
                logger.debug(
                    f"Extracted JSON from markdown for {step_name}: {json_content[:200]}..."
                )
            except Exception as e:
                logger.debug(f"No JSON markdown found for {step_name}: {e}")
                # If no markdown, assume the entire response is JSON
                json_content = response_text.strip()

            # Check if json_content is empty
            if not json_content:
                logger.error(f"Empty JSON content for step {step_name}")
                raise DAValueError(f"Empty JSON content for step {step_name}")

            logger.debug(f"Final JSON content for {step_name}: {json_content[:200]}...")

            # Parse JSON
            parsed_data = json.loads(json_content)

            print(f"parsed_data: {parsed_data}")

            # Validate based on step
            if step_name == "market_research":
                validated = MarketResearch(**parsed_data)
            elif step_name == "lbc":
                validated = LeanBusinessCanvas(**parsed_data)
            elif step_name == "persona":
                # Handle both single persona and personas array
                if "personas" in parsed_data:
                    validated_personas = [
                        UserPersona(**p) for p in parsed_data["personas"]
                    ]
                    validated = {
                        "personas": [
                            p.model_dump() if hasattr(p, "model_dump") else p.dict()
                            for p in validated_personas
                        ]
                    }
                else:
                    validated = UserPersona(**parsed_data)
            elif step_name == "swot":
                validated = SwotAnalysis(**parsed_data)
            elif step_name == "features":
                validated = FeaturesByCategory(**parsed_data)
            elif step_name == "roadmap":
                print(f"Roadmap parsed_data keys: {list(parsed_data.keys())}")
                print(f"Roadmap parsed_data: {parsed_data}")

                # Handle different possible field names for roadmap tasks
                tasks_data = None
                if "project_tasks" in parsed_data:
                    tasks_data = parsed_data["project_tasks"]
                elif "projectTasks" in parsed_data:
                    tasks_data = parsed_data["projectTasks"]
                elif "tasks" in parsed_data:
                    tasks_data = parsed_data["tasks"]
                elif "roadmap" in parsed_data:
                    tasks_data = parsed_data["roadmap"]
                else:
                    # If no recognized field, assume the entire data is the tasks array
                    if isinstance(parsed_data, list):
                        tasks_data = parsed_data
                    else:
                        tasks_data = []

                print(f"Extracted tasks_data: {tasks_data}")

                # Validate individual tasks first to ensure they all pass validation
                from src.models.brainstormer_models import RoadmapTask

                valid_task_objects = []

                print(f"Validating {len(tasks_data)} individual tasks...")
                for i, task_data in enumerate(tasks_data):
                    try:
                        # Validate each task individually and keep the validated object
                        task = RoadmapTask(**task_data)
                        valid_task_objects.append(task)
                        print(
                            f"✅ Task {i+1} '{task_data['task'][:30]}...' validation successful"
                        )
                    except Exception as task_error:
                        print(f"❌ Task {i+1} validation failed: {task_error}")
                        print(f"Problematic task data: {task_data}")
                        # Continue with other tasks, don't include this one

                print(
                    f"Successfully validated {len(valid_task_objects)} out of {len(tasks_data)} tasks"
                )

                # Skip Pydantic model validation and directly create the response structure
                print(
                    f"Creating roadmap response with {len(valid_task_objects)} valid task objects..."
                )

                # Convert validated task objects to dictionaries
                task_dicts = []
                for task_obj in valid_task_objects:
                    task_dict = (
                        task_obj.model_dump()
                        if hasattr(task_obj, "model_dump")
                        else task_obj.dict()
                    )
                    task_dicts.append(task_dict)

                # Create the response structure directly
                validated = {"project_tasks": task_dicts}
                print(
                    f"✅ Roadmap created successfully, final tasks count: {len(validated['project_tasks'])}"
                )
            else:
                # For unknown steps, return parsed JSON without validation
                return parsed_data

            print(
                "Validated : ",
                validated.model_dump()
                if hasattr(validated, "model_dump")
                else (validated.dict() if hasattr(validated, "dict") else validated),
            )

            return (
                validated.model_dump()
                if hasattr(validated, "model_dump")
                else (validated.dict() if hasattr(validated, "dict") else validated)
            )

        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse JSON for step {step_name}: {e}")
            raise DAValueError(f"Invalid JSON response for step {step_name}")
        except Exception as e:
            logger.error(f"Validation failed for step {step_name}: {e}")
            raise DAValueError(
                f"Response validation failed for step {step_name}: {str(e)}"
            )

    async def perform_web_search(
        self, query: str, user_signature: str, usecase: str = "web_search"
    ) -> Dict[str, Any]:
        """Calls the pipeline agent for web search and waits for completion via polling."""
        payload = {
            "pipelineId": "5775"
            if usecase == "web_search"
            else "5400",  # TODO: Make dynamic based on usecase,
            "userInputs": json.dumps({"user_request": query}),
            "user": user_signature,
            "priority": "0",
        }

        initial_response = await self.api_client.post_form(
            settings.pipeline_agent_service_endpoint, data=payload
        )

        execution_id = initial_response.get("workflowExecutionId")

        print(f"Web search execution ID: {initial_response}")
        if not execution_id:
            raise ValueError("No execution_id received from web search API")

        # Poll for completion for up to 60 seconds
        polling_url = f"{settings.pipeline_agent_service_endpoint}/{execution_id}/logs"
        max_attempts = 120  # 60 seconds with 0.5 second intervals

        for attempt in range(max_attempts):
            try:
                response = await self.api_client.get(polling_url)

                status = response.get("status")
                logs = response.get("workflowExecutionLogs", [])

                if status == "SUCCESS" and len(logs) >= 2:
                    # Get the second last item in workflowExecutionLogs
                    second_last_log = logs[-2]
                    stringified_json = second_last_log.get("logs", "{}")

                    # Parse the stringified JSON
                    try:
                        parsed_log = json.loads(stringified_json)
                        content = parsed_log.get("content", "")
                        return {"content": content}
                    except json.JSONDecodeError:
                        logger.warning(f"Failed to parse log JSON: {stringified_json}")
                        return {"content": stringified_json}

                elif status == "SUCCESS":
                    # If we don't have enough logs, return what we have
                    if logs:
                        last_log = logs[-1]
                        stringified_json = last_log.get("logs", "{}")
                        try:
                            parsed_log = json.loads(stringified_json)
                            content = parsed_log.get("content", "")
                            return {"content": content}
                        except json.JSONDecodeError:
                            return {"content": stringified_json}
                    return {"content": "No logs available"}

                # Continue polling if status is IN_PROGRESS or other
                await asyncio.sleep(0.5)

            except Exception as e:
                logger.warning(f"Polling attempt {attempt + 1} failed: {e}")
                await asyncio.sleep(0.5)

        # Timeout reached
        raise TimeoutError(
            f"Web search polling timed out after 60 seconds for execution_id: {execution_id}"
        )

    async def process_user_prompt(
        self, prompt: str, user_signature: str
    ) -> Dict[str, Any]:
        """Process a user prompt to identify details."""

        payload = {
            "mode": "EE_PS_BRAINSTORMER_INFO",
            "promptOverride": False,
            "useCaseIdentifier": "EE_PS_BRAINSTORMER_INFO@ASCENDION@EXPERIENCE_ENGINEERING@MICHELANGELO@CAPEX",
            "userSignature": user_signature,
            "image": None,
            "prompt": prompt,
        }

        # logger.debug(f"Payload for user prompt processing: {payload}")

        # Make API call
        response = await self.make_api_call(
            settings.individual_agent_service_endpoint, payload
        )

        response = DAResponse(**response)
        choices = response.response.choices
        if not choices:
            logger.error("No choices found in the DA response")
            raise DAServerError("No choices found in the DA response")

        response_text = choices[0].text

        json_content = get_content_inside_markdown(response_text, "json")
        parsed_data = json.loads(json_content)

        return parsed_data

    async def enhance_prompt(self, prompt: str, user_signature: str) -> Dict[str, Any]:
        """Process a user prompt to identify details."""

        payload = {
            "mode": "EE_PS_BRAINSTORMER_ENHANCE",
            "promptOverride": False,
            "useCaseIdentifier": "EE_PS_BRAINSTORMER_ENHANCE@ASCENDION@EXPERIENCE_ENGINEERING@MICHELANGELO@CAPEX",
            "userSignature": user_signature,
            "image": None,
            "prompt": prompt,
        }

        # logger.debug(f"Payload for user prompt processing: {payload}")

        # Make API call
        response = await self.make_api_call(
            settings.individual_agent_service_endpoint, payload
        )

        response = DAResponse(**response)
        choices = response.response.choices
        if not choices:
            logger.error("No choices found in the DA response")
            raise DAServerError("No choices found in the DA response")

        response_text = choices[0].text

        json_content = get_content_inside_markdown(response_text, "json")
        parsed_data = json.loads(json_content)

        return parsed_data

    async def regenerate_data_portion(
        self,
        run_id: str,
        current_data: Dict[str, Any],
        user_request: str,
        user_signature: str,
        step_name: str,
    ) -> Dict[str, Any]:
        """Regenerate a specific portion of data while maintaining the overall structure."""

        # Get the regenerate agent configuration
        agent_config = AGENT_NAMES.get("regenerate")
        if not agent_config:
            raise ValueError("No regenerate agent configuration found")

        step_data = {}

        if run_id and step_name:
            try:
                # Initialize Redis manager to load step data
                from src.state_manager import RedisManager

                redis = RedisManager()

                # Try to load from Redis first
                current_step_data = await redis.load_step_data(run_id, step_name)

                logger.info(f"current_step_data:  {current_step_data}")

                # Fallback to database if not in Redis and project service is available
                if current_step_data is None:
                    if self.project_service:
                        current_step_data = await self._load_step_from_db(
                            run_id, step_name
                        )
                        logger.info(f"DB_current_step_data:  {current_step_data}")
                    else:
                        logger.warning(
                            "ProjectService is not initialized. Skipping database fallback."
                        )

                if current_step_data:
                    step_data = json.dumps(current_step_data, indent=2)
                    logger.debug(
                        f"Loaded step data for '{step_name}' in regeneration context"
                    )
                else:
                    logger.warning(
                        f"Could not load step data for '{step_name}' in regeneration context"
                    )

            except Exception as e:
                logger.warning(f"Failed to load step data for regeneration: {e}")
                # Continue with empty step_data rather than failing

        # Format the prompt with context
        try:
            formatted_prompt = agent_config["prompt"].format(
                current_data=current_data,
                user_request=user_request,
                step_data=step_data,
            )
        except KeyError as e:
            raise ValueError(
                f"Missing required context key for regenerate prompt formatting: {e}"
            )

        payload = {
            "mode": agent_config["name"],
            "promptOverride": False,
            "useCaseIdentifier": f"{agent_config['name']}@ASCENDION@EXPERIENCE_ENGINEERING@MICHELANGELO@CAPEX",
            "userSignature": user_signature,
            "image": None,
            "prompt": formatted_prompt,
        }

        logger.debug(f"Regenerate payload: {payload}")

        # Make API call
        response = await self.make_api_call(
            settings.individual_agent_service_endpoint, payload
        )

        response = DAResponse(**response)
        choices = response.response.choices
        if not choices:
            logger.error("No choices found in the DA response for regeneration")
            raise DAServerError("No choices found in the DA response for regeneration")

        response_content = choices[0].text
        logger.debug(f"Raw regenerate response: {response_content[:500]}...")

        # Extract JSON content from markdown if present
        json_content = get_content_inside_markdown(response_content, "json")
        if not json_content:
            json_content = response_content

        logger.debug(f"Final JSON content for regeneration: {json_content[:200]}...")

        # Parse and return the regenerated data
        try:
            regenerated_data = json.loads(json_content)
            return regenerated_data
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse regenerated JSON: {e}")
            raise ValueError(f"Invalid JSON response from regeneration agent: {e}")


class ConversationalAgentService(AAVAAIService):
    def __init__(self, base_url: str):
        super().__init__(base_url)
        self.project_service = None  # Will be injected

    def set_project_service(self, project_service: ProjectService):
        """Inject project service dependency"""
        self.project_service = project_service

    async def _gather_context_for_conversational_agent(
        self,
        run_id: str,
        current_step: str,
        user_request: str,
        current_json_state: Dict[str, Any],
    ) -> Dict[str, Any]:
        """Gather context needed for conversational agent prompt formatting"""
        from src.config.pipeline_config import PIPELINE_CONTEXT_DEFINITION
        from src.state_manager import RedisManager

        context = {
            "current_json_state": json.dumps(current_json_state, indent=2),
        }

        # Get the context definition for this step
        step_info = PIPELINE_CONTEXT_DEFINITION.get(current_step, {})
        required_steps = step_info.get("context_needed", [])

        if not required_steps:
            return context

        logger.info(
            f"Gathering context for conversational agent step '{current_step}', requires: {required_steps}"
        )

        # Initialize Redis manager to load context data
        redis = RedisManager()

        for required_step in required_steps:
            try:
                # Try to load from Redis first
                step_data = await redis.load_step_data(run_id, required_step)

                # Fallback to database if not in Redis
                if not step_data and self.project_service:
                    step_data = await self._load_step_from_db(run_id, required_step)

                if step_data:
                    context[required_step] = step_data
                    logger.debug(
                        f"Added context for '{required_step}' to conversational agent"
                    )
                else:
                    logger.warning(
                        f"Could not load context data for step '{required_step}'"
                    )

            except Exception as e:
                logger.warning(
                    f"Failed to load context for step '{required_step}': {e}"
                )
        print("Context for conversational:", context)
        return context

    async def _load_step_from_db(self, run_id: str, step_name: str) -> Dict[str, Any]:
        """Load step data from database as fallback"""
        if not self.project_service:
            return None

        try:
            # Get project by run_id
            project = await self.project_service.get_project_by_run_id(run_id)
            if not project:
                logger.warning(f"No project found for run_id: {run_id}")
                return None

            project_id = project["id"]
            logger.debug(f"Found project_id: {project_id} for run_id: {run_id}")

            if step_name == "market_research":
                data = await self.project_service.get_market_research(project_id)
            elif step_name == "lbc":
                data = await self.project_service.get_lean_business_canvas(project_id)
            elif step_name == "persona":
                data = await self.project_service.get_user_personas(project_id)
            elif step_name == "swot":
                data = await self.project_service.get_swot_analysis(project_id)
            elif step_name == "features":
                data = await self.project_service.get_features(project_id)
            elif step_name == "roadmap":
                data = await self.project_service.get_roadmap_tasks(project_id)
            else:
                logger.warning(f"Unknown step name: {step_name}")
                return None

            return data
        except Exception as e:
            logger.error(f"Failed to load step '{step_name}' from database: {e}")
            return None

    async def _get_conversation_history(
        self,
        run_id: str,
        current_step: str,
        user_request: str = "",
        current_json_state: str = "",
    ) -> List[ConversationMessage]:
        """Fetch conversation history from database. Create initial message if none exists."""
        if not self.project_service:
            # Fallback to system message if no project service
            return [
                ConversationMessage(
                    content="You are a helpful assistant for editing product roadmap data.",
                    role="system",
                )
            ]
        agent_config = AGENT_NAMES.get("conversational")
        try:
            # Get project by run_id
            project = await self.project_service.get_project_by_run_id(run_id)
            if not project:
                return [
                    ConversationMessage(
                        content=agent_config["prompt"]
                        .replace("{{user_request}}", user_request)
                        .replace("{{current_json_state}}", current_json_state)
                        .replace("{{step_name}}", current_step),
                        role="system",
                    )
                ]

            project_id = project["id"]

            try:
                # Get conversation messages
                messages = await self.project_service.get_conversation_messages(
                    project_id, current_step
                )
                print("message:", messages, type(messages))

            except Exception as e:
                logger.error(f"Failed to fetch conversation messages: {e}")
                messages = None

            # Initialize conversation history list
            conversation_history = []

            # Create system message if no messages exist
            if not messages or messages == []:
                system_message = (
                    agent_config["prompt"]
                    .replace("{{user_request}}", user_request)
                    .replace("{{current_json_state}}", current_json_state)
                    .replace("{{step_name}}", current_step)
                )

                # Add the initial system message to the database
                await self.project_service.add_conversation_message(
                    project_id=project_id,
                    conversation_type=current_step,
                    content=system_message,
                    role="system",
                )
                return [
                    ConversationMessage(
                        content=system_message,
                        role="system",
                    )
                ]

            # Convert existing messages to ConversationMessage objects
            for msg in messages:
                conversation_history.append(
                    ConversationMessage(content=msg["content"], role=msg["role"])
                )

            return conversation_history

        except Exception as e:
            logger.error(f"Failed to fetch conversation history: {e}")
            # Return system message as fallback
            return [
                ConversationMessage(
                    content=agent_config["prompt"]
                    .replace("{user_request}", user_request)
                    .replace("{current_json_state}", current_json_state)
                    .replace("{step_name}", current_step),
                    role="system",
                )
            ]

    async def get_response(
        self,
        user_request: str,
        current_json_state: Dict[str, Any],
        run_id: str,
        current_step: str,
        user_signature: str,
        search_results: Dict[str, Any] = None,
    ) -> Dict[str, Any]:
        """Gets a conversational response for editing or delegation."""
        try:
            # Gather context for prompt formatting
            # context = await self._gather_context_for_conversational_agent(
            #     run_id, current_step, user_request, current_json_state
            # )

            # Get conversation history from database
            conversation_history = await self._get_conversation_history(
                run_id,
                current_step,
                user_request,
                json.dumps(current_json_state, indent=2),
            )

            # Add current user request
            conversation_history.append(
                ConversationMessage(content=user_request, role="user")
            )

            # Check if we have agent configuration for this step
            # agent_config = AGENT_NAMES.get(current_step)
            # if not agent_config:
            # Fallback to conversational agent config
            agent_config = AGENT_NAMES.get("conversational")
            if not agent_config:
                raise ValueError(
                    f"No agent configuration found for step: {current_step}"
                )

            print("Agent Config:", agent_config)

            # Format the prompt with context - handle missing keys gracefully
            try:
                formatted_prompt = agent_config["prompt"].replace(
                    "{{current_json_state}}", json.dumps(current_json_state)
                )
                logger.info(f"Successfully formatted prompt for step {current_step}")
            except KeyError as e:
                logger.warning(
                    f"Missing key in prompt formatting: {e}. Using fallback prompt."
                )
                # Fallback prompt for conversational agent
                formatted_prompt = f"User request: {user_request}\nCurrent state: {json.dumps(current_json_state, indent=2)}"

            logger.info(
                f"Formatted prompt for step {current_step}: {formatted_prompt}..."
            )

            payload = ConversationalAgentPayload(
                mode=agent_config["name"],
                useCaseIdentifier=f"{agent_config["name"]}@ASCENDION@EXPERIENCE_ENGINEERING@MICHELANGELO@CAPEX",
                userSignature=user_signature,
                conversations=conversation_history,
            )

            # Add search results if available
            # if search_results:
            #     search_content = f"Search results: {json.dumps(search_results)}"
            #     payload.conversations.append(ConversationMessage(content=search_content, role="system"))

            logger.info(
                f"Making API call to conversational agent for step: {current_step}"
            )

            # Make the API call with timeout handling
            try:
                logger.debug(
                    f"Making API call to: {settings.conversational_agent_service_endpoint}"
                )
                response = await self.make_api_call(
                    settings.conversational_agent_service_endpoint, payload.model_dump()
                )
                logger.debug(f"Raw response: {response}")
                logger.debug(
                    f"API call successful, response keys: {list(response.keys()) if isinstance(response, dict) else 'Not a dict'}"
                )
            except Exception as api_error:
                logger.error(
                    f"API call failed for conversational agent: {api_error}",
                    exc_info=True,
                )
                # Return a fallback response for edit operations
                return self._create_fallback_response(user_request, current_json_state)

            try:
                response = DAResponse(**response)
                choices = response.response.choices
                if not choices:
                    logger.error("No choices found in the DA response")
                    return self._create_fallback_response(
                        user_request, current_json_state
                    )
            except Exception as response_parse_error:
                logger.error(
                    f"Failed to parse DA response: {response_parse_error}",
                    exc_info=True,
                )
                logger.debug(f"Raw response that failed to parse: {response}")
                return self._create_fallback_response(user_request, current_json_state)

            response_text = choices[0].text

            if not response_text:
                logger.error("No text found in the choice")
                return self._create_fallback_response(user_request, current_json_state)

            logger.info(f"Received raw response text: {response_text[:200]}...")

            # Parse and validate the response
            parsed_response = self._parse_conversational_response(response_text)

            # Add to conversation history if parsing was successful and contains a summary
            try:
                if (
                    isinstance(parsed_response, dict)
                    and "message_to_user" in parsed_response
                ):
                    payload.conversations.append(
                        ConversationMessage(
                            content=parsed_response["message_to_user"], role="assistant"
                        )
                    )
            except Exception as e:
                logger.warning(f"Could not add response to conversation history: {e}")

            return parsed_response

        except Exception as e:
            logger.error(f"Unexpected error in get_response: {e}", exc_info=True)
            logger.debug(
                f"Error occurred for run_id: {run_id}, step: {current_step}, user_request: {user_request[:100]}..."
            )
            # Return fallback response instead of raising exception
            return self._create_fallback_response(user_request, current_json_state)

    def _parse_conversational_response(self, response_text: str) -> Dict[str, Any]:
        """Parse conversational response text and return validated dict"""
        try:
            logger.info(f"Parsing conversational response: {response_text[:500]}...")

            # Try multiple parsing strategies
            parsed_data = None

            # Strategy 1: Extract JSON from markdown
            try:
                json_content = get_content_inside_markdown(response_text, "json")
                parsed_data = json.loads(json_content)
                logger.info("Successfully parsed JSON from markdown")
            except Exception as e:
                logger.debug(f"Markdown parsing failed: {e}")

            # Strategy 2: Try parsing the entire response as JSON
            if parsed_data is None:
                try:
                    json_content = response_text.strip()
                    parsed_data = json.loads(json_content)
                    logger.info("Successfully parsed entire response as JSON")
                except Exception as e:
                    logger.debug(f"Direct JSON parsing failed: {e}")

            # Strategy 3: Look for JSON-like content in the response
            if parsed_data is None:
                try:
                    # Find JSON-like content between braces
                    import re

                    json_pattern = r"\{.*\}"
                    matches = re.findall(json_pattern, response_text, re.DOTALL)
                    if matches:
                        json_content = matches[0]
                        parsed_data = json.loads(json_content)
                        logger.info("Successfully extracted and parsed JSON pattern")
                except Exception as e:
                    logger.debug(f"Pattern-based JSON parsing failed: {e}")

            # Strategy 4: Create a structured response from the text
            if parsed_data is None:
                logger.warning(
                    "Could not parse as JSON, creating structured response from text"
                )
                parsed_data = {
                    "response_type": "clarification",
                    "message_to_user": response_text.strip(),
                    "payload": {},
                }

            # Validate the response structure
            if not isinstance(parsed_data, dict):
                raise ValueError("Response is not a dictionary")

            # Ensure required fields exist
            if "response_type" not in parsed_data:
                parsed_data["response_type"] = "clarification"
            if "message_to_user" not in parsed_data:
                parsed_data["message_to_user"] = "I've processed your request."
            if "payload" not in parsed_data:
                parsed_data["payload"] = {}

            logger.info(
                f"Successfully parsed conversational response: {parsed_data.get('response_type')}"
            )
            return parsed_data

        except Exception as e:
            logger.error(f"All parsing strategies failed: {e}", exc_info=True)
            logger.debug(f"Failed to parse response text: {response_text[:500]}...")
            # Return a safe fallback response
            return {
                "response_type": "clarification",
                "message_to_user": "I received your message but had trouble processing it. Please try rephrasing your request.",
                "payload": {},
            }

    def _create_fallback_response(
        self, user_request: str, current_json_state: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Create a fallback response when the conversational agent fails"""
        logger.info("Creating fallback response due to conversational agent failure")
        logger.debug(
            f"Current state keys: {list(current_json_state.keys()) if current_json_state else 'None'}"
        )
        return {
            "response_type": "clarification",
            "message_to_user": f"I understand you want to: '{user_request}'. However, I'm currently experiencing some technical difficulties. Please try again in a moment, or rephrase your request.",
            "payload": {},
        }
