"""
Tests for API endpoints.
"""

from unittest.mock import patch, AsyncMock, MagicMock
from fastapi.testclient import Test<PERSON><PERSON>
from fastapi import status
from httpx import AsyncClient


class TestAPIEndpoints:
    """Test class for API endpoints."""

    def test_root_endpoint(self, test_client: TestClient):
        """Test the root endpoint."""
        response = test_client.get("/")
        assert response.status_code == status.HTTP_200_OK
        assert response.json() == "Welcome to the FastAPI application!"

    def test_heartbeat_endpoint(self, test_client: TestClient):
        """Test the heartbeat endpoint."""
        response = test_client.get("/heartbeat")
        assert response.status_code == status.HTTP_200_OK
        assert response.json() == {"status": "ok"}

    @patch("src.routes.brainstormer_route.get_pipeline_manager")
    def test_start_pipeline_success(
        self, mock_get_pipeline_manager, test_client: TestClient, api_test_data
    ):
        """Test successful pipeline start."""
        # Setup mock
        mock_pipeline_manager = AsyncMock()
        mock_pipeline_manager.start_new_run.return_value = api_test_data[
            "sample_market_research"
        ]
        mock_get_pipeline_manager.return_value = mock_pipeline_manager

        # Make request
        response = test_client.post(
            "/api/v1/pipeline/start", json=api_test_data["start_request"]
        )

        # Assertions
        assert response.status_code == status.HTTP_201_CREATED
        response_data = response.json()

        assert "run_id" in response_data
        assert response_data["step"] == "market_research"
        assert "data" in response_data
        assert (
            response_data["data"]["market_summary"]
            == api_test_data["sample_market_research"]["market_summary"]
        )

        # Verify mock was called correctly
        mock_pipeline_manager.start_new_run.assert_called_once()
        call_args = mock_pipeline_manager.start_new_run.call_args
        assert (
            call_args[0][1] == api_test_data["start_request"]["user_idea"]
        )  # user_idea
        assert call_args[0][2] == "<EMAIL>"  # user_signature

    @patch("src.routes.brainstormer_route.get_pipeline_manager")
    async def test_start_pipeline_missing_user_idea(
        self, mock_get_pipeline_manager, test_client: AsyncClient
    ):
        """Test pipeline start with missing user_idea."""
        response = await test_client.post("/api/v1/pipeline/start", json={})

        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        response_data = response.json()
        assert "detail" in response_data

    @patch("src.routes.brainstormer_route.get_pipeline_manager")
    async def test_start_pipeline_internal_error(
        self, mock_get_pipeline_manager, test_client: AsyncClient, api_test_data
    ):
        """Test pipeline start with internal error."""
        # Setup mock to raise exception
        mock_pipeline_manager = AsyncMock()
        mock_pipeline_manager.start_new_run.side_effect = Exception(
            "Database connection failed"
        )
        mock_get_pipeline_manager.return_value = mock_pipeline_manager

        response = await test_client.post(
            "/api/v1/pipeline/start", json=api_test_data["start_request"]
        )

        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        response_data = response.json()
        assert "detail" in response_data
        assert "Database connection failed" in response_data["detail"]

    @patch("src.routes.brainstormer_route.get_pipeline_manager")
    async def test_next_pipeline_step_success(
        self, mock_get_pipeline_manager, test_client: AsyncClient, api_test_data
    ):
        """Test successful next pipeline step."""
        # Setup mock
        mock_pipeline_manager = AsyncMock()
        next_step_data = {
            "step": "lbc",
            "data": {
                "problem": ["Problem 1", "Problem 2"],
                "solution": ["Solution 1", "Solution 2"],
            },
        }
        mock_pipeline_manager.trigger_next_step.return_value = next_step_data
        mock_get_pipeline_manager.return_value = mock_pipeline_manager

        # Make request
        response = await test_client.post(
            "/api/v1/pipeline/next", json=api_test_data["next_step_request"]
        )

        # Assertions
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert response_data == next_step_data

        # Verify mock was called correctly
        mock_pipeline_manager.trigger_next_step.assert_called_once()
        call_args = mock_pipeline_manager.trigger_next_step.call_args
        assert call_args[0][0] == api_test_data["next_step_request"]["run_id"]
        assert call_args[0][1] == api_test_data["next_step_request"]["current_step"]
        assert call_args[0][2] == "<EMAIL>"

    @patch("src.routes.brainstormer_route.get_pipeline_manager")
    async def test_next_pipeline_step_invalid_step(
        self, mock_get_pipeline_manager, test_client: AsyncClient, api_test_data
    ):
        """Test next pipeline step with invalid current step."""
        # Setup mock to raise ValueError
        mock_pipeline_manager = AsyncMock()
        mock_pipeline_manager.trigger_next_step.side_effect = ValueError(
            "Invalid current step: invalid_step"
        )
        mock_get_pipeline_manager.return_value = mock_pipeline_manager

        invalid_request = api_test_data["next_step_request"].copy()
        invalid_request["current_step"] = "invalid_step"

        response = await test_client.post("/api/v1/pipeline/next", json=invalid_request)

        assert response.status_code == status.HTTP_400_BAD_REQUEST
        response_data = response.json()
        assert "Invalid current step" in response_data["detail"]

    @patch("src.routes.brainstormer_route.get_pipeline_manager")
    async def test_next_pipeline_step_last_step(
        self, mock_get_pipeline_manager, test_client: AsyncClient, api_test_data
    ):
        """Test next pipeline step when already at last step."""
        # Setup mock to raise ValueError for last step
        mock_pipeline_manager = AsyncMock()
        mock_pipeline_manager.trigger_next_step.side_effect = ValueError(
            "Already at the last step of the pipeline."
        )
        mock_get_pipeline_manager.return_value = mock_pipeline_manager

        last_step_request = api_test_data["next_step_request"].copy()
        last_step_request["current_step"] = "roadmap"

        response = await test_client.post(
            "/api/v1/pipeline/next", json=last_step_request
        )

        assert response.status_code == status.HTTP_400_BAD_REQUEST
        response_data = response.json()
        assert "Already at the last step" in response_data["detail"]

    @patch("src.routes.brainstormer_route.get_pipeline_manager")
    async def test_chat_handler_success(
        self, mock_get_pipeline_manager, test_client: AsyncClient, api_test_data
    ):
        """Test successful chat message handling."""
        # Setup mock
        mock_pipeline_manager = AsyncMock()
        mock_response = MagicMock()
        mock_response.dict.return_value = api_test_data[
            "sample_conversational_response"
        ]
        mock_pipeline_manager.handle_chat_message.return_value = mock_response
        mock_get_pipeline_manager.return_value = mock_pipeline_manager

        # Make request
        response = await test_client.post(
            "/api/v1/pipeline/chat", json=api_test_data["chat_request"]
        )

        # Assertions
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert response_data == api_test_data["sample_conversational_response"]

        # Verify mock was called correctly
        mock_pipeline_manager.handle_chat_message.assert_called_once()
        call_args = mock_pipeline_manager.handle_chat_message.call_args
        assert call_args[1]["run_id"] == api_test_data["chat_request"]["run_id"]
        assert (
            call_args[1]["current_step"]
            == api_test_data["chat_request"]["current_step"]
        )
        assert call_args[1]["user_message"] == api_test_data["chat_request"]["message"]
        assert call_args[1]["user_signature"] == "<EMAIL>"

    @patch("src.routes.brainstormer_route.get_pipeline_manager")
    async def test_chat_handler_invalid_run_id(
        self, mock_get_pipeline_manager, test_client: AsyncClient, api_test_data
    ):
        """Test chat handler with invalid run_id."""
        # Setup mock to raise ValueError
        mock_pipeline_manager = AsyncMock()
        mock_pipeline_manager.handle_chat_message.side_effect = ValueError(
            "Invalid run_id or step_name"
        )
        mock_get_pipeline_manager.return_value = mock_pipeline_manager

        invalid_request = api_test_data["chat_request"].copy()
        invalid_request["run_id"] = "invalid-run-id"

        response = await test_client.post("/api/v1/pipeline/chat", json=invalid_request)

        assert response.status_code == status.HTTP_404_NOT_FOUND
        response_data = response.json()
        assert "Invalid run_id or step_name" in response_data["detail"]

    @patch("src.routes.brainstormer_route.get_pipeline_manager")
    async def test_chat_handler_missing_fields(
        self, mock_get_pipeline_manager, test_client: AsyncClient
    ):
        """Test chat handler with missing required fields."""
        response = await test_client.post(
            "/api/v1/pipeline/chat",
            json={"run_id": "test-id"},  # Missing current_step and message
        )

        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        response_data = response.json()
        assert "detail" in response_data

    async def test_invalid_endpoint(self, test_client: AsyncClient):
        """Test request to non-existent endpoint."""
        response = await test_client.get("/api/v1/nonexistent")
        assert response.status_code == status.HTTP_404_NOT_FOUND

    async def test_invalid_method(self, test_client: AsyncClient):
        """Test invalid HTTP method on existing endpoint."""
        response = await test_client.get("/api/v1/pipeline/start")
        assert response.status_code == status.HTTP_405_METHOD_NOT_ALLOWED
