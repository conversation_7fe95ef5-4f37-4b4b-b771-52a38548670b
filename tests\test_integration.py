"""
Integration tests for the complete database workflow.
"""

import pytest
import asyncpg
from uuid import UUID
from src.services.database.project_service import ProjectService
from src.services.database.user_service import UserService
from tests.utils.db_cleanup import DatabaseCleaner


@pytest.mark.asyncio
class TestDatabaseIntegration:
    """Integration tests for the complete database workflow."""

    @pytest.fixture
    async def services(self, test_db_pool: asyncpg.Pool):
        """Create service instances for testing."""
        return {
            "project": ProjectService(test_db_pool),
            "user": UserService(test_db_pool),
            "cleaner": DatabaseCleaner(test_db_pool),
        }

    async def test_complete_project_workflow(
        self,
        services,
        clean_db,
        sample_project_data,
        sample_market_research_data,
        sample_lbc_data,
        sample_personas_data,
        sample_swot_data,
        sample_features_data,
        sample_roadmap_tasks_data,
    ):
        """Test the complete project creation and data population workflow."""
        project_service = services["project"]
        user_service = services["user"]
        cleaner = services["cleaner"]

        # Step 1: Create a user
        user = await user_service.create_user(
            email="<EMAIL>", full_name="Integration Test User"
        )
        user_id = UUID(user["user_id"])

        # Step 2: Create a project
        project = await project_service.create_project(
            name=sample_project_data["name"],
            description=sample_project_data["description"],
            created_by=user_id,
            run_id=sample_project_data["run_id"],
        )
        project_id = project["id"]

        # Step 3: Add market research data
        mr_id = await project_service.update_market_research(
            project_id=project_id,
            market_summary=sample_market_research_data["market_summary"],
            identified_gaps=sample_market_research_data["identified_gaps"],
            competitors=sample_market_research_data["competitors"],
        )

        # Step 4: Add lean business canvas data
        lbc_id = await project_service.update_lean_business_canvas(
            project_id=project_id, lbc_data=sample_lbc_data
        )

        # Step 5: Add user personas
        persona_ids = await project_service.update_user_personas(
            project_id=project_id, personas=sample_personas_data
        )

        # Step 6: Add SWOT analysis
        swot_id = await project_service.update_swot_analysis(
            project_id=project_id, swot_data=sample_swot_data
        )

        # Step 7: Add features
        feature_ids = await project_service.update_features(
            project_id=project_id, features=sample_features_data
        )

        # Step 8: Add roadmap tasks
        task_ids = await project_service.update_roadmap_tasks(
            project_id=project_id, tasks=sample_roadmap_tasks_data
        )

        # Step 9: Add conversation messages
        conversation_types = [
            "market_research",
            "lbc",
            "persona",
            "swot",
            "features",
            "roadmap",
        ]
        for conv_type in conversation_types:
            await project_service.add_conversation_message(
                project_id=project_id,
                conversation_type=conv_type,
                content=f"Test message for {conv_type}",
                role="user",
            )
            await project_service.add_conversation_message(
                project_id=project_id,
                conversation_type=conv_type,
                content=f"Response for {conv_type}",
                role="assistant",
            )

        # Verify all data was created
        assert mr_id is not None
        assert lbc_id is not None
        assert len(persona_ids) == len(sample_personas_data)
        assert swot_id is not None
        assert len(feature_ids) == len(sample_features_data)
        assert len(task_ids) == len(sample_roadmap_tasks_data)

        # Step 10: Retrieve and verify all data
        retrieved_project = await project_service.get_project_by_run_id(
            sample_project_data["run_id"]
        )
        assert retrieved_project is not None
        assert retrieved_project["name"] == sample_project_data["name"]

        retrieved_mr = await project_service.get_market_research(project_id)
        assert retrieved_mr is not None
        assert len(retrieved_mr["competitors"]) == len(
            sample_market_research_data["competitors"]
        )

        retrieved_lbc = await project_service.get_lean_business_canvas(project_id)
        assert retrieved_lbc is not None
        assert retrieved_lbc["problem"] == sample_lbc_data["problem"]

        retrieved_personas = await project_service.get_user_personas(project_id)
        assert retrieved_personas is not None

        retrieved_features = await project_service.get_features(project_id)
        assert retrieved_features is not None
        assert len(retrieved_features) == len(sample_features_data)

        retrieved_tasks = await project_service.get_roadmap_tasks(project_id)
        assert retrieved_tasks is not None
        assert len(retrieved_tasks) == len(sample_roadmap_tasks_data)

        # Verify conversation messages
        for conv_type in conversation_types:
            messages = await project_service.get_conversation_messages(
                project_id, conv_type
            )
            assert len(messages) == 2  # user + assistant message

        # Step 11: Get project data summary
        summary = await cleaner.get_project_data_summary(project_id)
        assert summary["market_research"] == 1
        assert summary["competitors"] == len(sample_market_research_data["competitors"])
        assert summary["lean_business_canvas"] == 1
        assert summary["user_personas"] == len(sample_personas_data)
        assert summary["swot_analysis"] == 1
        assert summary["features"] == len(sample_features_data)
        assert summary["roadmap_tasks"] == len(sample_roadmap_tasks_data)
        assert (
            summary["conversations"] == len(conversation_types) * 2
        )  # 2 messages per type

        # Step 12: Test data cleanup
        await cleaner.clean_project_data(project_id)

        # Verify project data is cleaned
        summary_after_cleanup = await cleaner.get_project_data_summary(project_id)
        assert all(count == 0 for count in summary_after_cleanup.values())

        # Verify project is deleted
        retrieved_project_after_cleanup = await project_service.get_project_by_run_id(
            sample_project_data["run_id"]
        )
        assert retrieved_project_after_cleanup is None

        # User should still exist
        retrieved_user = await user_service.get_user_by_id(user_id)
        assert retrieved_user is not None

        # Clean up user
        user_deleted = await user_service.delete_user(user_id)
        assert user_deleted is True

    async def test_data_update_workflow(
        self,
        services,
        clean_db,
        sample_project_data,
        sample_market_research_data,
        sample_lbc_data,
    ):
        """Test updating existing data."""
        project_service = services["project"]
        user_service = services["user"]

        # Create user and project
        user = await user_service.create_user(
            email="<EMAIL>", full_name="Update Test User"
        )
        user_id = UUID(user["user_id"])

        project = await project_service.create_project(
            name=sample_project_data["name"],
            description=sample_project_data["description"],
            created_by=user_id,
        )
        project_id = project["id"]

        # Add initial market research
        await project_service.update_market_research(
            project_id=project_id,
            market_summary="Initial summary",
            identified_gaps="Initial gaps",
            competitors=[
                {
                    "name": "Initial Competitor",
                    "url": "http://initial.com",
                    "strengths": "Initial strengths",
                }
            ],
        )

        # Add initial LBC data
        initial_lbc = {"problem": ["Initial problem"], "solution": ["Initial solution"]}
        await project_service.update_lean_business_canvas(
            project_id=project_id, lbc_data=initial_lbc
        )

        # Update market research with new data
        await project_service.update_market_research(
            project_id=project_id,
            market_summary=sample_market_research_data["market_summary"],
            identified_gaps=sample_market_research_data["identified_gaps"],
            competitors=sample_market_research_data["competitors"],
        )

        # Update LBC data
        await project_service.update_lean_business_canvas(
            project_id=project_id, lbc_data=sample_lbc_data
        )

        # Verify updates
        updated_mr = await project_service.get_market_research(project_id)
        assert (
            updated_mr["market_summary"]
            == sample_market_research_data["market_summary"]
        )
        assert len(updated_mr["competitors"]) == len(
            sample_market_research_data["competitors"]
        )

        updated_lbc = await project_service.get_lean_business_canvas(project_id)
        assert updated_lbc["problem"] == sample_lbc_data["problem"]
        assert updated_lbc["solution"] == sample_lbc_data["solution"]

    async def test_multiple_projects_isolation(self, services, clean_db):
        """Test that data from different projects is properly isolated."""
        project_service = services["project"]
        user_service = services["user"]

        # Create user
        user = await user_service.create_user(
            email="<EMAIL>", full_name="Isolation Test User"
        )
        user_id = UUID(user["user_id"])

        # Create two projects
        project1 = await project_service.create_project(
            name="Project 1", description="First project", created_by=user_id
        )
        project2 = await project_service.create_project(
            name="Project 2", description="Second project", created_by=user_id
        )

        # Add different data to each project
        await project_service.update_market_research(
            project_id=project1["id"],
            market_summary="Project 1 summary",
            identified_gaps="Project 1 gaps",
            competitors=[
                {
                    "name": "P1 Competitor",
                    "url": "http://p1.com",
                    "strengths": "P1 strengths",
                }
            ],
        )

        await project_service.update_market_research(
            project_id=project2["id"],
            market_summary="Project 2 summary",
            identified_gaps="Project 2 gaps",
            competitors=[
                {
                    "name": "P2 Competitor",
                    "url": "http://p2.com",
                    "strengths": "P2 strengths",
                }
            ],
        )

        # Verify data isolation
        p1_data = await project_service.get_market_research(project1["id"])
        p2_data = await project_service.get_market_research(project2["id"])

        assert p1_data["market_summary"] == "Project 1 summary"
        assert p2_data["market_summary"] == "Project 2 summary"
        assert p1_data["competitors"][0]["name"] == "P1 Competitor"
        assert p2_data["competitors"][0]["name"] == "P2 Competitor"

        # Clean up one project and verify the other is unaffected
        cleaner = services["cleaner"]
        await cleaner.clean_project_data(project1["id"])

        # Project 1 data should be gone
        p1_data_after = await project_service.get_market_research(project1["id"])
        assert p1_data_after is None

        # Project 2 data should still exist
        p2_data_after = await project_service.get_market_research(project2["id"])
        assert p2_data_after is not None
        assert p2_data_after["market_summary"] == "Project 2 summary"
