"""
Demo test to show the test infrastructure works without database.
"""

import pytest
from unittest.mock import AsyncMock, MagicMock
from uuid import uuid4
from src.services.database.user_service import UserService
from src.services.database.project_service import ProjectService


class TestDatabaseServicesMock:
    """Demo tests using mocked database connections."""

    @pytest.fixture
    def mock_db_pool(self):
        """Create a mock database pool."""
        mock_pool = MagicMock()
        mock_conn = AsyncMock()

        # Setup async context managers properly
        mock_pool.acquire.return_value.__aenter__.return_value = mock_conn

        # Mock the transaction context manager
        mock_transaction = AsyncMock()
        mock_conn.transaction.return_value = mock_transaction
        mock_transaction.__aenter__ = AsyncMock(return_value=mock_transaction)
        mock_transaction.__aexit__ = AsyncMock(return_value=None)

        return mock_pool

    @pytest.fixture
    def user_service(self, mock_db_pool):
        """Create UserService with mocked database."""
        return UserService(mock_db_pool)

    @pytest.fixture
    def project_service(self, mock_db_pool):
        """Create ProjectService with mocked database."""
        return ProjectService(mock_db_pool)

    @pytest.mark.asyncio
    async def test_user_service_create_user_mock(self, user_service, mock_db_pool):
        """Test UserService.create_user with mocked database."""
        # Setup mock
        test_user_id = uuid4()
        mock_conn = mock_db_pool.acquire.return_value.__aenter__.return_value
        mock_conn.fetchval.return_value = test_user_id

        # Call the method
        result = await user_service.create_user(
            email="<EMAIL>", full_name="Test User"
        )

        # Verify the result
        assert result["user_id"] == str(test_user_id)
        assert result["email"] == "<EMAIL>"
        assert result["full_name"] == "Test User"

        # Verify database was called correctly
        mock_conn.fetchval.assert_called_once()
        call_args = mock_conn.fetchval.call_args
        assert "INSERT INTO brainstormer.users" in call_args[0][0]

    @pytest.mark.asyncio
    async def test_project_service_create_project_mock(
        self, project_service, mock_db_pool
    ):
        """Test ProjectService.create_project with mocked database."""
        # Setup mock
        test_project_id = 123
        test_user_id = uuid4()
        test_run_id = uuid4()

        mock_conn = mock_db_pool.acquire.return_value.__aenter__.return_value
        mock_conn.fetchval.return_value = test_project_id

        # Call the method
        result = await project_service.create_project(
            name="Test Project",
            description="A test project",
            created_by=test_user_id,
            run_id=test_run_id,
        )

        # Verify the result
        assert result["id"] == test_project_id
        assert result["name"] == "Test Project"
        assert result["description"] == "A test project"
        assert result["created_by"] == str(test_user_id)
        assert result["run_id"] == str(test_run_id)

        # Verify database was called correctly
        mock_conn.fetchval.assert_called_once()
        call_args = mock_conn.fetchval.call_args
        assert "INSERT INTO brainstormer.projects" in call_args[0][0]

    def test_project_service_methods_exist(self):
        """Test that ProjectService has all expected methods for market research."""
        from src.services.database.project_service import ProjectService

        # Test that the method exists and is callable
        assert hasattr(ProjectService, "update_market_research")
        assert callable(getattr(ProjectService, "update_market_research"))

        # Test method signature (this will help catch signature changes)
        import inspect

        sig = inspect.signature(ProjectService.update_market_research)
        expected_params = [
            "self",
            "project_id",
            "market_summary",
            "identified_gaps",
            "competitors",
        ]
        actual_params = list(sig.parameters.keys())
        assert actual_params == expected_params

    def test_sample_data_fixtures(
        self,
        sample_project_data,
        sample_market_research_data,
        sample_lbc_data,
        sample_personas_data,
        sample_swot_data,
        sample_features_data,
        sample_roadmap_tasks_data,
    ):
        """Test that all sample data fixtures are properly structured."""
        # Test project data
        assert "name" in sample_project_data
        assert "description" in sample_project_data
        assert "run_id" in sample_project_data

        # Test market research data
        assert "market_summary" in sample_market_research_data
        assert "identified_gaps" in sample_market_research_data
        assert "competitors" in sample_market_research_data
        assert len(sample_market_research_data["competitors"]) > 0

        # Test LBC data
        expected_lbc_fields = [
            "problem",
            "solution",
            "key_partners",
            "value_proposition",
            "customer_segments",
            "revenue_streams",
            "key_metrics",
            "alternatives",
            "solution_tenants",
        ]
        for field in expected_lbc_fields:
            assert field in sample_lbc_data
            assert isinstance(sample_lbc_data[field], list)

        # Test personas data
        assert len(sample_personas_data) > 0
        persona = sample_personas_data[0]
        expected_persona_fields = [
            "name",
            "role",
            "age",
            "education",
            "status",
            "location",
            "tech_literacy",
            "avatar",
            "quote",
            "personality",
            "pain_points",
            "goals",
            "motivation",
            "expectations",
            "devices",
        ]
        for field in expected_persona_fields:
            assert field in persona

        # Test SWOT data
        expected_swot_fields = ["strengths", "weaknesses", "opportunities", "threats"]
        for field in expected_swot_fields:
            assert field in sample_swot_data
            assert isinstance(sample_swot_data[field], list)
            assert len(sample_swot_data[field]) > 0

        # Test features data
        assert len(sample_features_data) > 0
        feature = sample_features_data[0]
        expected_feature_fields = [
            "category",
            "title",
            "description",
            "tenant",
            "justification",
            "tags",
            "moscow_rank",
        ]
        for field in expected_feature_fields:
            assert field in feature

        # Test roadmap tasks data
        assert len(sample_roadmap_tasks_data) > 0
        task = sample_roadmap_tasks_data[0]
        expected_task_fields = [
            "task",
            "description",
            "priority",
            "duration",
            "quarter",
        ]
        for field in expected_task_fields:
            assert field in task

    def test_database_cleanup_utility_structure(self):
        """Test that the database cleanup utility can be imported and has expected methods."""
        from tests.utils.db_cleanup import DatabaseCleaner

        # Check that the class exists and has expected methods
        expected_methods = [
            "clean_all_tables",
            "clean_project_data",
            "clean_user_data",
            "get_table_counts",
            "verify_clean_state",
            "clean_conversation_data",
            "clean_market_research_data",
            "clean_features_data",
            "clean_roadmap_data",
            "clean_personas_data",
            "clean_lbc_data",
            "clean_swot_data",
            "get_project_data_summary",
        ]

        for method_name in expected_methods:
            assert hasattr(
                DatabaseCleaner, method_name
            ), f"Missing method: {method_name}"

    def test_service_imports_and_structure(self):
        """Test that all services can be imported and have expected methods."""
        # Test UserService
        expected_user_methods = [
            "create_user",
            "get_user_by_id",
            "get_user_by_email",
            "update_user",
            "delete_user",
            "list_users",
        ]
        for method_name in expected_user_methods:
            assert hasattr(
                UserService, method_name
            ), f"UserService missing method: {method_name}"

        # Test ProjectService
        expected_project_methods = [
            "create_project",
            "get_project_by_run_id",
            "update_market_research",
            "update_lean_business_canvas",
            "update_user_personas",
            "update_swot_analysis",
            "update_features",
            "update_roadmap_tasks",
            "add_conversation_message",
            "get_conversation_messages",
            "get_market_research",
            "get_lean_business_canvas",
            "get_user_personas",
            "get_swot_analysis",
            "get_features",
            "get_roadmap_tasks",
        ]
        for method_name in expected_project_methods:
            assert hasattr(
                ProjectService, method_name
            ), f"ProjectService missing method: {method_name}"
