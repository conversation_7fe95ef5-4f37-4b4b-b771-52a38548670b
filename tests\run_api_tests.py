#!/usr/bin/env python3
"""
API Test Runner Script for FastAPI endpoints.
"""

import os
import sys
import subprocess
from pathlib import Path


def check_dependencies():
    """Check if required dependencies are installed."""
    print("Checking API test dependencies...")

    try:
        print("✅ httpx installed")
        print("✅ pytest-mock installed")
        return True
    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        print("Please install with: uv add --dev httpx pytest-mock")
        return False


def run_api_tests():
    """Run the API test suite."""
    print("Running API endpoint tests...")
    print("=" * 50)

    # Test categories to run
    test_categories = [
        ("Basic API Tests", "tests/api/test_endpoints.py"),
        ("API Integration Tests", "tests/api/test_integration.py"),
        ("API Validation Tests", "tests/api/test_validation.py"),
    ]

    all_passed = True
    results = {}

    for category_name, test_file in test_categories:
        print(f"\n🧪 Running {category_name}...")
        print("-" * 40)

        cmd = [
            sys.executable,
            "-m",
            "pytest",
            test_file,
            "-v",
            "--tb=short",
            "--asyncio-mode=auto",
            "-x",  # Stop on first failure for faster feedback
        ]

        try:
            result = subprocess.run(cmd, capture_output=True, text=True, check=False)

            if result.returncode == 0:
                print(f"✅ {category_name} - ALL PASSED")
                # Count passed tests
                passed_count = result.stdout.count(" PASSED")
                results[category_name] = {"status": "PASSED", "count": passed_count}
            else:
                print(f"❌ {category_name} - SOME FAILED")
                print("STDOUT:", result.stdout[-500:])  # Last 500 chars
                print("STDERR:", result.stderr[-500:])  # Last 500 chars
                results[category_name] = {"status": "FAILED", "count": 0}
                all_passed = False

        except Exception as e:
            print(f"❌ Error running {category_name}: {e}")
            results[category_name] = {"status": "ERROR", "count": 0}
            all_passed = False

    return all_passed, results


def run_specific_test_examples():
    """Run specific test examples to demonstrate functionality."""
    print("\n🎯 Running Specific Test Examples...")
    print("=" * 50)

    specific_tests = [
        (
            "Root Endpoint Test",
            "tests/api/test_endpoints.py::TestAPIEndpoints::test_root_endpoint",
        ),
        (
            "Heartbeat Test",
            "tests/api/test_endpoints.py::TestAPIEndpoints::test_heartbeat_endpoint",
        ),
        (
            "Start Pipeline Success",
            "tests/api/test_endpoints.py::TestAPIEndpoints::test_start_pipeline_success",
        ),
        (
            "Chat Handler Success",
            "tests/api/test_endpoints.py::TestAPIEndpoints::test_chat_handler_success",
        ),
        (
            "Request Validation",
            "tests/api/test_validation.py::TestAPIValidation::test_start_pipeline_request_validation",
        ),
    ]

    for test_name, test_path in specific_tests:
        print(f"\n🔍 {test_name}...")

        cmd = [
            sys.executable,
            "-m",
            "pytest",
            test_path,
            "-v",
            "--tb=line",
            "--asyncio-mode=auto",
        ]

        try:
            result = subprocess.run(cmd, capture_output=True, text=True, check=False)

            if result.returncode == 0:
                print(f"✅ {test_name} - PASSED")
            else:
                print(f"❌ {test_name} - FAILED")
                print("Error:", result.stdout.split("\n")[-3:-1])  # Last few lines

        except Exception as e:
            print(f"❌ Error running {test_name}: {e}")


def test_chat_endpoint_with_specific_run_id(run_id: str):
    """Test the chat endpoint with a specific run_id."""
    print(f"\n🎯 Testing Chat Endpoint with run_id: {run_id}")
    print("=" * 60)

    import asyncio
    import httpx

    async def make_chat_request():
        """Make a request to the chat endpoint."""
        base_url = "http://localhost:8000"

        # Test data for the chat request
        chat_data = {
            "run_id": run_id,
            "current_step": "market_research",  # You may need to adjust this
            "message": "Can you provide more details about the current analysis?",
        }

        try:
            async with httpx.AsyncClient() as client:
                print(f"🔗 Making request to: {base_url}/api/v1/pipeline/chat")
                print(f"📝 Request data: {chat_data}")

                response = await client.post(
                    f"{base_url}/api/v1/pipeline/chat", json=chat_data, timeout=30.0
                )

                print(f"📊 Response Status: {response.status_code}")
                print(f"📄 Response Headers: {dict(response.headers)}")

                if response.status_code == 200:
                    response_data = response.json()
                    print("✅ SUCCESS! Chat endpoint responded correctly")
                    print(f"📋 Response Data: {response_data}")
                    return True
                else:
                    print(f"❌ FAILED! Status code: {response.status_code}")
                    try:
                        error_data = response.json()
                        print(f"📋 Error Data: {error_data}")
                    except Exception:
                        print(f"📋 Raw Response: {response.text}")
                    return False

        except httpx.ConnectError:
            print("❌ CONNECTION ERROR: Could not connect to the API server")
            print("💡 Make sure the API server is running on http://localhost:8000")
            print("💡 You can start it with: uvicorn src.main:app --reload")
            return False
        except Exception as e:
            print(f"❌ UNEXPECTED ERROR: {e}")
            return False

    # Run the async function
    try:
        return asyncio.run(make_chat_request())
    except Exception as e:
        print(f"❌ Error running async test: {e}")
        return False


def print_test_summary(results):
    """Print a summary of test results."""
    print("\n" + "=" * 60)
    print("📊 API TEST SUMMARY")
    print("=" * 60)

    total_passed = 0
    total_categories = len(results)
    passed_categories = 0

    for category, result in results.items():
        status_icon = "✅" if result["status"] == "PASSED" else "❌"
        print(f"{status_icon} {category}: {result['status']} ({result['count']} tests)")

        if result["status"] == "PASSED":
            passed_categories += 1
            total_passed += result["count"]

    print("-" * 60)
    print(f"📈 Categories Passed: {passed_categories}/{total_categories}")
    print(f"📈 Total Tests Passed: {total_passed}")

    if passed_categories == total_categories:
        print("\n🎉 ALL API TESTS PASSED!")
        print("✅ All endpoints are working correctly")
        print("✅ Request validation is working")
        print("✅ Error handling is working")
        print("✅ Integration workflows are working")
    else:
        print(f"\n⚠️  {total_categories - passed_categories} categories had failures")
        print("Check the output above for details")


def print_api_endpoints_info():
    """Print information about the API endpoints being tested."""
    print("🔗 API ENDPOINTS BEING TESTED:")
    print("=" * 50)

    endpoints = [
        ("GET", "/", "Root endpoint"),
        ("GET", "/heartbeat", "Health check endpoint"),
        ("POST", "/api/v1/pipeline/start", "Start new pipeline"),
        ("POST", "/api/v1/pipeline/next", "Trigger next pipeline step"),
        ("POST", "/api/v1/pipeline/chat", "Handle chat messages"),
    ]

    for method, path, description in endpoints:
        print(f"  {method:6} {path:30} - {description}")

    print("\n🧪 TEST CATEGORIES:")
    print("-" * 30)
    print("  • Endpoint functionality tests")
    print("  • Request/response validation")
    print("  • Error handling and edge cases")
    print("  • Integration workflow tests")
    print("  • Concurrent request handling")
    print("  • Input validation and sanitization")


def main():
    """Main function."""
    print("API Endpoint Test Runner")
    print("=" * 50)

    # Change to project root directory
    project_root = Path(__file__).parent
    os.chdir(project_root)

    # Check if specific run_id is provided as command line argument
    specific_run_id = None
    if len(sys.argv) > 1:
        if sys.argv[1] == "--chat-test" and len(sys.argv) > 2:
            specific_run_id = sys.argv[2]
            print(
                f"🎯 Running specific chat endpoint test with run_id: {specific_run_id}"
            )
            success = test_chat_endpoint_with_specific_run_id(specific_run_id)
            if success:
                print("\n✅ Chat endpoint test completed successfully!")
                sys.exit(0)
            else:
                print("\n❌ Chat endpoint test failed!")
                sys.exit(1)

    # Print API info
    print_api_endpoints_info()

    # Check dependencies
    if not check_dependencies():
        print("\n❌ Dependencies not met. Please install required packages.")
        sys.exit(1)

    print("\n🚀 Starting API Tests...")

    # Run main test suite
    all_passed, results = run_api_tests()

    # Run specific examples
    run_specific_test_examples()

    # Print summary
    print_test_summary(results)

    if all_passed:
        print("\n" + "=" * 60)
        print("🎯 API TESTING COMPLETE - ALL TESTS PASSED!")
        print("=" * 60)
        print("✅ All API endpoints are functioning correctly")
        print("✅ Request validation is working properly")
        print("✅ Error handling is robust")
        print("✅ Integration workflows are successful")
        print("✅ The API is ready for production use!")
        print("=" * 60)
        sys.exit(0)
    else:
        print("\n" + "=" * 60)
        print("⚠️  API TESTING COMPLETE - SOME ISSUES FOUND")
        print("=" * 60)
        print("❌ Some tests failed - check output above for details")
        print("🔧 Fix the issues and run tests again")
        print("=" * 60)
        sys.exit(1)


if __name__ == "__main__":
    main()
