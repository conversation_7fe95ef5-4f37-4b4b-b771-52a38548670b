#!/usr/bin/env python3
"""
Standalone test script for the /pipeline/chat endpoint.
Usage: python test_chat_endpoint.py <run_id>
"""

import asyncio
import sys
import httpx
import json


async def check_pipeline_status(run_id: str) -> dict:
    """Check if a pipeline run exists and what steps are available."""
    base_url = "http://localhost:8000"

    print("🔍 Checking pipeline status...")
    print(f"🆔 Run ID: {run_id}")

    # Try to get information about the pipeline by testing different steps
    steps_to_check = [
        "market_research",
        "lbc",
        "persona",
        "swot",
        "features",
        "roadmap",
    ]
    available_steps = []

    for step in steps_to_check:
        try:
            async with httpx.AsyncClient() as client:
                # Try a simple chat request to see if the step exists
                test_data = {"run_id": run_id, "current_step": step, "message": "test"}

                response = await client.post(
                    f"{base_url}/api/v1/pipeline/chat", json=test_data, timeout=5.0
                )

                if (
                    response.status_code != 404
                ):  # If not "not found", the step might exist
                    available_steps.append(
                        {
                            "step": step,
                            "status_code": response.status_code,
                            "response": response.text[:200] + "..."
                            if len(response.text) > 200
                            else response.text,
                        }
                    )

        except Exception as e:
            print(f"Error checking step {step}: {e}")

    return {"run_id": run_id, "available_steps": available_steps}


async def test_chat_endpoint(
    run_id: str, current_step: str = "market_research", message: str = None
) -> bool:
    """
    Test the chat endpoint with a specific run_id.

    Args:
        run_id: The pipeline run ID to test with
        current_step: The current step in the pipeline (default: "market_research")
        message: The message to send (default: a test message)

    Returns:
        bool: True if the test was successful, False otherwise
    """
    base_url = "http://localhost:8000"

    # Default test message if none provided
    if message is None:
        message = "Can you provide more details about the current analysis?"

    # Test data for the chat request
    chat_data = {"run_id": run_id, "current_step": current_step, "message": message}

    print("🚀 Testing Chat Endpoint")
    print("=" * 50)
    print(f"🔗 Endpoint: {base_url}/api/v1/pipeline/chat")
    print("📝 Request Data:")
    print(json.dumps(chat_data, indent=2))
    print("-" * 50)

    try:
        async with httpx.AsyncClient() as client:
            print("📡 Sending request...")

            response = await client.post(
                f"{base_url}/api/v1/pipeline/chat", json=chat_data, timeout=30.0
            )

            print(f"📊 Response Status: {response.status_code}")
            print("📄 Response Headers:")
            for key, value in response.headers.items():
                print(f"  {key}: {value}")
            print("-" * 50)

            if response.status_code == 200:
                print("✅ SUCCESS! Chat endpoint responded correctly")
                try:
                    response_data = response.json()
                    print("📋 Response Data:")
                    print(json.dumps(response_data, indent=2))
                except json.JSONDecodeError:
                    print("📋 Raw Response (not JSON):")
                    print(response.text)
                return True

            elif response.status_code == 404:
                print("❌ NOT FOUND (404)")
                print("💡 This could mean:")
                print("   - The run_id doesn't exist")
                print("   - The current_step is invalid")
                print("   - The pipeline is not in a valid state")
                try:
                    error_data = response.json()
                    print(f"📋 Error Details: {json.dumps(error_data, indent=2)}")
                except Exception:
                    print(f"📋 Raw Error Response: {response.text}")
                return False

            elif response.status_code == 422:
                print("❌ VALIDATION ERROR (422)")
                print("💡 The request data format is invalid")
                try:
                    error_data = response.json()
                    print(f"📋 Validation Errors: {json.dumps(error_data, indent=2)}")
                except Exception:
                    print(f"📋 Raw Error Response: {response.text}")
                return False

            else:
                print(f"❌ FAILED! Status code: {response.status_code}")
                try:
                    error_data = response.json()
                    print(f"📋 Error Data: {json.dumps(error_data, indent=2)}")
                except Exception:
                    print(f"📋 Raw Response: {response.text}")
                return False

    except httpx.ConnectError:
        print("❌ CONNECTION ERROR: Could not connect to the API server")
        print("💡 Make sure the API server is running on http://localhost:8000")
        print("💡 You can start it with: uvicorn src.main:app --reload")
        return False

    except httpx.TimeoutException:
        print("❌ TIMEOUT ERROR: The request took too long to complete")
        print("💡 The server might be processing a complex request")
        return False

    except Exception as e:
        print(f"❌ UNEXPECTED ERROR: {e}")
        return False


def main():
    """Main function."""
    if len(sys.argv) < 2:
        print("Usage: python test_chat_endpoint.py <run_id> [current_step] [message]")
        print("\nExample:")
        print("  python test_chat_endpoint.py 5e98560c-9826-47c3-a6ca-f1f8b528f9ec")
        print(
            "  python test_chat_endpoint.py 5e98560c-9826-47c3-a6ca-f1f8b528f9ec market_research"
        )
        print(
            "  python test_chat_endpoint.py 5e98560c-9826-47c3-a6ca-f1f8b528f9ec market_research 'Tell me more about competitors'"
        )
        print("\nAdd --check-status flag to check pipeline status first:")
        print(
            "  python test_chat_endpoint.py 5e98560c-9826-47c3-a6ca-f1f8b528f9ec --check-status"
        )
        sys.exit(1)

    run_id = sys.argv[1]

    # Check if user wants to check status first
    if len(sys.argv) > 2 and sys.argv[2] == "--check-status":
        print(f"🎯 Checking pipeline status for run_id: {run_id}")
        status_info = asyncio.run(check_pipeline_status(run_id))

        print("\n📊 Pipeline Status Report:")
        print("=" * 50)
        print(f"🆔 Run ID: {status_info['run_id']}")
        print(f"📋 Available Steps: {len(status_info['available_steps'])}")

        for step_info in status_info["available_steps"]:
            print(f"\n🔸 Step: {step_info['step']}")
            print(f"   Status Code: {step_info['status_code']}")
            print(f"   Response: {step_info['response']}")

        if not status_info["available_steps"]:
            print("❌ No available steps found for this run_id")
            print("💡 This might mean the run_id doesn't exist or has no data")

        sys.exit(0)

    current_step = sys.argv[2] if len(sys.argv) > 2 else "market_research"
    message = sys.argv[3] if len(sys.argv) > 3 else None

    print(f"🎯 Testing chat endpoint with run_id: {run_id}")

    # First check pipeline status
    print("\n🔍 Checking pipeline status first...")
    status_info = asyncio.run(check_pipeline_status(run_id))

    if not status_info["available_steps"]:
        print("❌ No available steps found for this run_id")
        print("💡 The run_id might not exist or have no data")
        print("🔧 Try creating a new pipeline first with /api/v1/pipeline/start")
        sys.exit(1)

    print(f"✅ Found {len(status_info['available_steps'])} available steps")

    # Run the test
    success = asyncio.run(test_chat_endpoint(run_id, current_step, message))

    if success:
        print("\n" + "=" * 50)
        print("🎉 TEST COMPLETED SUCCESSFULLY!")
        print("✅ The chat endpoint is working correctly")
        print("=" * 50)
        sys.exit(0)
    else:
        print("\n" + "=" * 50)
        print("❌ TEST FAILED!")
        print("🔧 Check the error messages above for details")
        print("=" * 50)
        sys.exit(1)


if __name__ == "__main__":
    main()
