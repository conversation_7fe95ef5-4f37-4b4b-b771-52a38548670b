"""
Tests for API request validation and edge cases.
"""

import pytest
from httpx import AsyncClient
from fastapi import status
from unittest.mock import patch, AsyncMock


@pytest.mark.asyncio
class TestAPIValidation:
    """Test class for API request validation and edge cases."""

    async def test_start_pipeline_request_validation(self, test_client: AsyncClient):
        """Test request validation for start pipeline endpoint."""

        # Test empty request body
        response = await test_client.post("/api/v1/pipeline/start", json={})
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        error_detail = response.json()["detail"]
        assert any("user_idea" in str(error) for error in error_detail)

        # Test missing user_idea field
        response = await test_client.post(
            "/api/v1/pipeline/start", json={"other_field": "value"}
        )
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

        # Test empty user_idea
        response = await test_client.post(
            "/api/v1/pipeline/start", json={"user_idea": ""}
        )
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

        # Test user_idea with only whitespace
        response = await test_client.post(
            "/api/v1/pipeline/start", json={"user_idea": "   "}
        )
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

        # Test null user_idea
        response = await test_client.post(
            "/api/v1/pipeline/start", json={"user_idea": None}
        )
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

        # Test non-string user_idea
        response = await test_client.post(
            "/api/v1/pipeline/start", json={"user_idea": 123}
        )
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    async def test_next_step_request_validation(self, test_client: AsyncClient):
        """Test request validation for next step endpoint."""

        # Test empty request body
        response = await test_client.post("/api/v1/pipeline/next", json={})
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

        # Test missing run_id
        response = await test_client.post(
            "/api/v1/pipeline/next", json={"current_step": "market_research"}
        )
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

        # Test missing current_step
        response = await test_client.post(
            "/api/v1/pipeline/next", json={"run_id": "test-run-id"}
        )
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

        # Test empty run_id
        response = await test_client.post(
            "/api/v1/pipeline/next",
            json={"run_id": "", "current_step": "market_research"},
        )
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

        # Test empty current_step
        response = await test_client.post(
            "/api/v1/pipeline/next", json={"run_id": "test-run-id", "current_step": ""}
        )
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

        # Test null values
        response = await test_client.post(
            "/api/v1/pipeline/next", json={"run_id": None, "current_step": None}
        )
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    async def test_chat_request_validation(self, test_client: AsyncClient):
        """Test request validation for chat endpoint."""

        # Test empty request body
        response = await test_client.post("/api/v1/pipeline/chat", json={})
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

        # Test missing fields
        response = await test_client.post(
            "/api/v1/pipeline/chat", json={"run_id": "test-run-id"}
        )
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

        response = await test_client.post(
            "/api/v1/pipeline/chat",
            json={"run_id": "test-run-id", "current_step": "market_research"},
        )
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

        # Test empty message
        response = await test_client.post(
            "/api/v1/pipeline/chat",
            json={
                "run_id": "test-run-id",
                "current_step": "market_research",
                "message": "",
            },
        )
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

        # Test whitespace-only message
        response = await test_client.post(
            "/api/v1/pipeline/chat",
            json={
                "run_id": "test-run-id",
                "current_step": "market_research",
                "message": "   ",
            },
        )
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    async def test_invalid_json_requests(self, test_client: AsyncClient):
        """Test handling of invalid JSON in requests."""

        # Test malformed JSON
        response = await test_client.post(
            "/api/v1/pipeline/start",
            content='{"user_idea": "test"',  # Missing closing brace
            headers={"Content-Type": "application/json"},
        )
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

        # Test non-JSON content type
        response = await test_client.post(
            "/api/v1/pipeline/start",
            content="user_idea=test",
            headers={"Content-Type": "application/x-www-form-urlencoded"},
        )
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    async def test_large_request_payloads(self, test_client: AsyncClient):
        """Test handling of large request payloads."""

        # Test very long user_idea
        long_user_idea = "A" * 10000  # 10KB string
        response = await test_client.post(
            "/api/v1/pipeline/start", json={"user_idea": long_user_idea}
        )
        # Should still be valid (assuming no length limit is enforced)
        assert response.status_code in [
            status.HTTP_201_CREATED,
            status.HTTP_500_INTERNAL_SERVER_ERROR,
        ]

        # Test very long chat message
        long_message = "B" * 5000  # 5KB string
        response = await test_client.post(
            "/api/v1/pipeline/chat",
            json={
                "run_id": "test-run-id",
                "current_step": "market_research",
                "message": long_message,
            },
        )
        # Should still be valid (assuming no length limit is enforced)
        assert response.status_code in [
            status.HTTP_200_OK,
            status.HTTP_404_NOT_FOUND,
            status.HTTP_500_INTERNAL_SERVER_ERROR,
        ]

    async def test_special_characters_in_requests(self, test_client: AsyncClient):
        """Test handling of special characters in requests."""

        # Test Unicode characters
        unicode_idea = "I want to build an app with émojis 🚀 and spëcial characters"
        response = await test_client.post(
            "/api/v1/pipeline/start", json={"user_idea": unicode_idea}
        )
        assert response.status_code in [
            status.HTTP_201_CREATED,
            status.HTTP_500_INTERNAL_SERVER_ERROR,
        ]

        # Test HTML/XML-like content
        html_idea = "I want to build <script>alert('test')</script> a secure app"
        response = await test_client.post(
            "/api/v1/pipeline/start", json={"user_idea": html_idea}
        )
        assert response.status_code in [
            status.HTTP_201_CREATED,
            status.HTTP_500_INTERNAL_SERVER_ERROR,
        ]

        # Test SQL injection-like content
        sql_idea = "I want to build an app'; DROP TABLE users; --"
        response = await test_client.post(
            "/api/v1/pipeline/start", json={"user_idea": sql_idea}
        )
        assert response.status_code in [
            status.HTTP_201_CREATED,
            status.HTTP_500_INTERNAL_SERVER_ERROR,
        ]

    async def test_content_type_validation(self, test_client: AsyncClient):
        """Test content type validation."""

        # Test missing content type
        response = await test_client.post(
            "/api/v1/pipeline/start", content='{"user_idea": "test"}', headers={}
        )
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

        # Test wrong content type
        response = await test_client.post(
            "/api/v1/pipeline/start",
            content='{"user_idea": "test"}',
            headers={"Content-Type": "text/plain"},
        )
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    @patch("src.routes.brainstormer_route.get_pipeline_manager")
    async def test_uuid_format_validation(
        self, mock_get_pipeline_manager, test_client: AsyncClient
    ):
        """Test UUID format validation in run_id."""

        # Setup mock
        mock_pipeline_manager = AsyncMock()
        mock_pipeline_manager.trigger_next_step.side_effect = ValueError(
            "Invalid run_id format"
        )
        mock_get_pipeline_manager.return_value = mock_pipeline_manager

        # Test invalid UUID format
        invalid_uuids = [
            "not-a-uuid",
            "12345",
            "abc-def-ghi",
            "123e4567-e89b-12d3-a456-42661417400",  # Too short
            "123e4567-e89b-12d3-a456-4266141740000",  # Too long
        ]

        for invalid_uuid in invalid_uuids:
            response = await test_client.post(
                "/api/v1/pipeline/next",
                json={"run_id": invalid_uuid, "current_step": "market_research"},
            )
            # The API should handle this gracefully, either through validation or error handling
            assert response.status_code in [
                status.HTTP_400_BAD_REQUEST,
                status.HTTP_422_UNPROCESSABLE_ENTITY,
                status.HTTP_500_INTERNAL_SERVER_ERROR,
            ]

    async def test_cors_headers(self, test_client: AsyncClient):
        """Test CORS headers are present."""

        # Test preflight request
        response = await test_client.options("/api/v1/pipeline/start")
        # FastAPI should handle OPTIONS requests for CORS
        assert response.status_code in [
            status.HTTP_200_OK,
            status.HTTP_405_METHOD_NOT_ALLOWED,
        ]

        # Test actual request has CORS headers
        response = await test_client.get("/heartbeat")
        # Note: In test environment, CORS headers might not be set the same way
        assert response.status_code == status.HTTP_200_OK

    async def test_request_size_limits(self, test_client: AsyncClient):
        """Test request size limits."""

        # Test extremely large request (if size limits are enforced)
        huge_data = {"user_idea": "A" * 1000000}  # 1MB string

        try:
            response = await test_client.post(
                "/api/v1/pipeline/start",
                json=huge_data,
                timeout=30.0,  # Increase timeout for large requests
            )
            # Should either succeed or fail gracefully
            assert response.status_code in [
                status.HTTP_201_CREATED,
                status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                status.HTTP_500_INTERNAL_SERVER_ERROR,
            ]
        except Exception:
            # If the request fails due to size limits, that's acceptable
            pass
