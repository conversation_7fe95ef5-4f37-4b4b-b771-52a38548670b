#!/usr/bin/env python3
"""
Test script to verify the fixes for the conversational agent issues.
"""

import asyncio
from unittest.mock import AsyncMock
from src.services.aava_service import ConversationalAgentService
from src.config.pipeline_config import AGENT_NAMES


async def test_context_gathering():
    """Test that context gathering works correctly for LBC step"""
    print("Testing context gathering for LBC step...")

    # Create a mock conversational agent
    agent = ConversationalAgentService("http://test.com")

    # Mock the project service
    mock_project_service = AsyncMock()
    mock_project_service.get_project_by_run_id.return_value = {"id": 123}
    mock_project_service.get_market_research.return_value = {
        "market_summary": "Test market summary",
        "competitors": [{"name": "Competitor 1", "url": "http://comp1.com"}],
    }
    agent.set_project_service(mock_project_service)

    # Mock Redis manager
    from unittest.mock import patch

    with patch("src.services.aava_service.RedisManager") as mock_redis_class:
        mock_redis = AsyncMock()
        mock_redis.load_step_data.return_value = None  # Force fallback to DB
        mock_redis_class.return_value = mock_redis

        # Test context gathering
        context = await agent._gather_context_for_conversational_agent(
            run_id="test-run-id",
            current_step="lbc",
            user_request="Test request",
            current_json_state={"test": "data"},
        )

        print(f"Context keys: {list(context.keys())}")
        print(f"Has market_research: {'market_research' in context}")
        print(f"Has user_request: {'user_request' in context}")
        print(f"Has current_json_state: {'current_json_state' in context}")

        # Test prompt formatting
        agent_config = AGENT_NAMES.get("lbc")
        if agent_config:
            try:
                formatted_prompt = agent_config["prompt"].format(**context)
                print("✅ Prompt formatting successful!")
                print(f"Formatted prompt length: {len(formatted_prompt)}")
            except KeyError as e:
                print(f"❌ Prompt formatting failed: {e}")
        else:
            print("❌ No agent config found for LBC")


def test_response_parsing():
    """Test response parsing logic"""
    print("\nTesting response parsing...")

    agent = ConversationalAgentService("http://test.com")

    # Test case 1: Valid JSON response
    test_response_1 = """```json
{
    "response_type": "edit",
    "message_to_user": "I've updated the data",
    "payload": {"updated": true}
}
```"""

    result_1 = agent._parse_conversational_response(test_response_1)
    print(f"Test 1 - Valid JSON: {result_1.get('response_type')} ✅")

    # Test case 2: Plain JSON without markdown
    test_response_2 = """{"response_type": "clarification", "message_to_user": "Please clarify", "payload": {}}"""

    result_2 = agent._parse_conversational_response(test_response_2)
    print(f"Test 2 - Plain JSON: {result_2.get('response_type')} ✅")

    # Test case 3: Invalid response (should fallback)
    test_response_3 = "This is not JSON at all"

    result_3 = agent._parse_conversational_response(test_response_3)
    print(f"Test 3 - Invalid response: {result_3.get('response_type')} ✅")


async def main():
    """Run all tests"""
    print("🧪 Testing conversational agent fixes...\n")

    await test_context_gathering()
    test_response_parsing()

    print("\n✅ All tests completed!")


if __name__ == "__main__":
    asyncio.run(main())
