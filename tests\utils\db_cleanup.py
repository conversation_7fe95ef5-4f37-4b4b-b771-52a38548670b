"""
Database cleanup utilities for tests.
"""

import asyncpg
from typing import Optional
from uuid import UUID


class DatabaseCleaner:
    """Utility class for cleaning up test data from the database."""

    def __init__(self, db_pool: asyncpg.Pool):
        self.db_pool = db_pool

    async def clean_all_tables(self):
        """Clean all tables in the correct order to respect foreign key constraints."""
        async with self.db_pool.acquire() as conn:
            # Clean tables in reverse dependency order
            tables_to_clean = [
                "brainstormer.project_conversations",
                "brainstormer.roadmap_tasks",
                "brainstormer.features",
                "brainstormer.swot_analysis",
                "brainstormer.user_personas",
                "brainstormer.lean_business_canvas",
                "brainstormer.competitors",
                "brainstormer.market_research",
                "brainstormer.projects",
                "brainstormer.users",
            ]

            for table in tables_to_clean:
                await conn.execute(f"DELETE FROM {table}")

    async def clean_project_data(self, project_id: int):
        """Clean all data related to a specific project."""
        async with self.db_pool.acquire() as conn:
            # Clean project-related tables
            project_tables = [
                "brainstormer.project_conversations",
                "brainstormer.roadmap_tasks",
                "brainstormer.features",
                "brainstormer.swot_analysis",
                "brainstormer.user_personas",
                "brainstormer.lean_business_canvas",
                "brainstormer.competitors",
                "brainstormer.market_research",
            ]

            for table in project_tables:
                if table == "brainstormer.competitors":
                    # Competitors are linked through market_research
                    await conn.execute(
                        """
                        DELETE FROM brainstormer.competitors
                        WHERE market_research_id IN (
                            SELECT id FROM brainstormer.market_research
                            WHERE project_id = $1
                        )
                        """,
                        project_id,
                    )
                else:
                    await conn.execute(
                        f"DELETE FROM {table} WHERE project_id = $1", project_id
                    )

            # Finally, delete the project itself
            await conn.execute(
                "DELETE FROM brainstormer.projects WHERE id = $1", project_id
            )

    async def clean_user_data(self, user_id: UUID):
        """Clean all data related to a specific user."""
        async with self.db_pool.acquire() as conn:
            # First, get all projects created by this user
            project_ids = await conn.fetch(
                "SELECT id FROM brainstormer.projects WHERE created_by = $1", user_id
            )

            # Clean each project's data
            for project_row in project_ids:
                await self.clean_project_data(project_row["id"])

            # Finally, delete the user
            await conn.execute(
                "DELETE FROM brainstormer.users WHERE user_id = $1", user_id
            )

    async def get_table_counts(self) -> dict:
        """Get row counts for all tables (useful for debugging tests)."""
        async with self.db_pool.acquire() as conn:
            tables = [
                "brainstormer.users",
                "brainstormer.projects",
                "brainstormer.market_research",
                "brainstormer.competitors",
                "brainstormer.lean_business_canvas",
                "brainstormer.user_personas",
                "brainstormer.swot_analysis",
                "brainstormer.features",
                "brainstormer.roadmap_tasks",
                "brainstormer.project_conversations",
            ]

            counts = {}
            for table in tables:
                count = await conn.fetchval(f"SELECT COUNT(*) FROM {table}")
                table_name = table.split(".")[-1]  # Remove schema prefix
                counts[table_name] = count

            return counts

    async def verify_clean_state(self) -> bool:
        """Verify that all tables are empty (useful for test setup verification)."""
        counts = await self.get_table_counts()
        return all(count == 0 for count in counts.values())

    async def clean_conversation_data(
        self, project_id: int, conversation_type: Optional[str] = None
    ):
        """Clean conversation data for a project, optionally filtered by conversation type."""
        async with self.db_pool.acquire() as conn:
            if conversation_type:
                await conn.execute(
                    """
                    DELETE FROM brainstormer.project_conversations
                    WHERE project_id = $1 AND conversation_type = $2
                    """,
                    project_id,
                    conversation_type,
                )
            else:
                await conn.execute(
                    "DELETE FROM brainstormer.project_conversations WHERE project_id = $1",
                    project_id,
                )

    async def clean_market_research_data(self, project_id: int):
        """Clean market research data for a specific project."""
        async with self.db_pool.acquire() as conn:
            # First delete competitors (they reference market_research)
            await conn.execute(
                """
                DELETE FROM brainstormer.competitors
                WHERE market_research_id IN (
                    SELECT id FROM brainstormer.market_research
                    WHERE project_id = $1
                )
                """,
                project_id,
            )

            # Then delete market research
            await conn.execute(
                "DELETE FROM brainstormer.market_research WHERE project_id = $1",
                project_id,
            )

    async def clean_features_data(self, project_id: int):
        """Clean features data for a specific project."""
        async with self.db_pool.acquire() as conn:
            await conn.execute(
                "DELETE FROM brainstormer.features WHERE project_id = $1", project_id
            )

    async def clean_roadmap_data(self, project_id: int):
        """Clean roadmap tasks data for a specific project."""
        async with self.db_pool.acquire() as conn:
            await conn.execute(
                "DELETE FROM brainstormer.roadmap_tasks WHERE project_id = $1",
                project_id,
            )

    async def clean_personas_data(self, project_id: int):
        """Clean user personas data for a specific project."""
        async with self.db_pool.acquire() as conn:
            await conn.execute(
                "DELETE FROM brainstormer.user_personas WHERE project_id = $1",
                project_id,
            )

    async def clean_lbc_data(self, project_id: int):
        """Clean lean business canvas data for a specific project."""
        async with self.db_pool.acquire() as conn:
            await conn.execute(
                "DELETE FROM brainstormer.lean_business_canvas WHERE project_id = $1",
                project_id,
            )

    async def clean_swot_data(self, project_id: int):
        """Clean SWOT analysis data for a specific project."""
        async with self.db_pool.acquire() as conn:
            await conn.execute(
                "DELETE FROM brainstormer.swot_analysis WHERE project_id = $1",
                project_id,
            )

    async def get_project_data_summary(self, project_id: int) -> dict:
        """Get a summary of all data associated with a project."""
        async with self.db_pool.acquire() as conn:
            summary = {}

            # Count records in each project-related table
            tables_queries = {
                "market_research": "SELECT COUNT(*) FROM brainstormer.market_research WHERE project_id = $1",
                "competitors": """
                    SELECT COUNT(*) FROM brainstormer.competitors c
                    JOIN brainstormer.market_research mr ON c.market_research_id = mr.id
                    WHERE mr.project_id = $1
                """,
                "lean_business_canvas": "SELECT COUNT(*) FROM brainstormer.lean_business_canvas WHERE project_id = $1",
                "user_personas": "SELECT COUNT(*) FROM brainstormer.user_personas WHERE project_id = $1",
                "swot_analysis": "SELECT COUNT(*) FROM brainstormer.swot_analysis WHERE project_id = $1",
                "features": "SELECT COUNT(*) FROM brainstormer.features WHERE project_id = $1",
                "roadmap_tasks": "SELECT COUNT(*) FROM brainstormer.roadmap_tasks WHERE project_id = $1",
                "conversations": "SELECT COUNT(*) FROM brainstormer.project_conversations WHERE project_id = $1",
            }

            for table_name, query in tables_queries.items():
                count = await conn.fetchval(query, project_id)
                summary[table_name] = count

            return summary
