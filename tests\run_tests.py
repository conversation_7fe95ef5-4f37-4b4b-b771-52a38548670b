#!/usr/bin/env python3
"""
Test runner script for database services.
"""

import os
import sys
import asyncio
import subprocess
from pathlib import Path


def check_database_connection():
    """Check if database is available for testing."""
    db_host = os.getenv("TEST_DB_HOST", "localhost")
    db_port = os.getenv("TEST_DB_PORT", "5432")
    db_user = os.getenv("TEST_DB_USER", "postgres")
    db_name = os.getenv("TEST_DB_NAME", "product_studio_test")

    print(f"Checking database connection to {db_host}:{db_port}/{db_name}")

    try:
        import asyncpg

        async def test_connection():
            try:
                conn = await asyncpg.connect(
                    host=db_host,
                    port=int(db_port),
                    user=db_user,
                    password=os.getenv("TEST_DB_PASSWORD", ""),
                    database=db_name,
                )
                await conn.close()
                return True
            except Exception as e:
                print(f"Database connection failed: {e}")
                return False

        return asyncio.run(test_connection())
    except ImportError:
        print("asyncpg not installed")
        return False


def setup_test_environment():
    """Set up test environment variables."""
    # Set default test database environment variables if not already set
    test_env_vars = {
        "TEST_DB_HOST": "localhost",
        "TEST_DB_PORT": "5432",
        "TEST_DB_USER": "postgres",
        "TEST_DB_PASSWORD": "",
        "TEST_DB_NAME": "product_studio_test",
    }

    for key, default_value in test_env_vars.items():
        if key not in os.environ:
            os.environ[key] = default_value
            print(f"Set {key}={default_value}")


def run_tests():
    """Run the test suite."""
    print("Setting up test environment...")
    setup_test_environment()

    print("Checking database connection...")
    if not check_database_connection():
        print("\n" + "=" * 50)
        print("DATABASE CONNECTION FAILED")
        print("=" * 50)
        print("To run these tests, you need a PostgreSQL database.")
        print("You can either:")
        print("1. Set up a local PostgreSQL instance")
        print(
            "2. Use Docker: docker run --name test-postgres -e POSTGRES_PASSWORD=test -p 5432:5432 -d postgres"
        )
        print("3. Set environment variables to point to your test database:")
        print("   - TEST_DB_HOST")
        print("   - TEST_DB_PORT")
        print("   - TEST_DB_USER")
        print("   - TEST_DB_PASSWORD")
        print("   - TEST_DB_NAME")
        print("\nNote: The test database will be created if it doesn't exist.")
        print("=" * 50)
        return False

    print("Database connection successful!")
    print("Running tests...")

    # Run pytest
    cmd = [
        sys.executable,
        "-m",
        "pytest",
        "tests/",
        "-v",
        "--tb=short",
        "--asyncio-mode=auto",
    ]

    try:
        result = subprocess.run(cmd, check=False)
        return result.returncode == 0
    except Exception as e:
        print(f"Error running tests: {e}")
        return False


def main():
    """Main function."""
    print("Database Services Test Runner")
    print("=" * 40)

    # Change to project root directory
    project_root = Path(__file__).parent
    os.chdir(project_root)

    success = run_tests()

    if success:
        print("\n" + "=" * 40)
        print("✅ ALL TESTS PASSED!")
        print("Database services are working correctly.")
        print("Data is being written, read, and deleted properly.")
        print("=" * 40)
        sys.exit(0)
    else:
        print("\n" + "=" * 40)
        print("❌ SOME TESTS FAILED!")
        print("Check the output above for details.")
        print("=" * 40)
        sys.exit(1)


if __name__ == "__main__":
    main()
