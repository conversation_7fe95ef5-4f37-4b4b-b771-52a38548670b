import os
from typing import Optional
from pydantic import Field
from src.utils.api_helper.api_client import APIClient
from dotenv import load_dotenv
load_dotenv()
try:
    from pydantic_settings import BaseSettings
except ImportError:
    from pydantic import BaseSettings
from src.utils.logger import AppLogger


class Settings(BaseSettings):
    app_name: str = "Product Studio Server"
    debug: bool = False

    # API endpoints that will be fetched on startup
    base_url: Optional[str] = None
    pipeline_agent_service_endpoint: Optional[str] = None
    individual_agent_service_endpoint: Optional[str] = None
    conversational_agent_service_endpoint: Optional[str] = None
    redis_url: Optional[str] = None

    # Keys
    access_key: Optional[str] = None

    # Config service settings
    config_service_url: Optional[str] = Field(default=os.getenv("APP_CONFIG_API_URL"))
    service_name: str = Field(default="prod-studio-api")

    # Database settings
    db_host: Optional[str] = Field(default=os.getenv("DB_HOST"))
    db_port: Optional[int] = Field(default=int(os.getenv("DB_PORT")) if os.getenv("DB_PORT") else None)
    db_user: Optional[str] = Field(default=os.getenv("DB_USER"))
    db_password: Optional[str] = Field(default=os.getenv("DB_PASSWORD"))
    db_name: Optional[str] = Field(default=os.getenv("DB_NAME"))

    class Config:
        env_file = ".env"
        case_sensitive = False
        extra = "allow"  # Allow extra fields from environment

    async def fetch_runtime_config(self):
        """Fetch configuration from external service on startup"""
        logger = AppLogger("Settings").get_logger()
        logger.info("Starting fetch_runtime_config...")

        try:
            logger.info("Setting initial values from environment variables...")
            self.base_url = os.getenv("PLATFORM_BASE_URL")
            self.pipeline_agent_service_endpoint = os.getenv("PIPELINE_AGENT_SERVICE_ENDPOINT")
            self.individual_agent_service_endpoint = os.getenv("INDIVIDUAL_AGENT_SERVICE_ENDPOINT")
            self.conversational_agent_service_endpoint = (
                os.getenv("INDIVIDUAL_AGENT_SERVICE_ENDPOINT") + "/chat"
                if os.getenv("INDIVIDUAL_AGENT_SERVICE_ENDPOINT")
                else None
            )
            self.redis_url = os.getenv("REDIS_URL")
            self.access_key = os.getenv("ACCESS_KEY")

            logger.info(f"Fetching config from API: {self.config_service_url}")
            logger.info(f"Service name: {self.service_name}")

            if not self.config_service_url:
                logger.warning("No config_service_url available, skipping API fetch")
                return

            api_client = APIClient(base_url=self.config_service_url)
            endpoint = f"api/v1/config/multi-app?applications={self.service_name}"
            logger.info(f"Full API endpoint: {self.config_service_url}/{endpoint}")

            config_data = await api_client.get(endpoint)
            logger.info(f"Config data received with {len(config_data) if config_data else 0} items")

            if config_data and isinstance(config_data, list):
                logger.info("Config data received, converting to dictionary...")
                
                config_dict = {}
                for item in config_data:
                    if isinstance(item, dict) and 'configKey' in item and 'configValue' in item:
                        config_dict[item['configKey']] = item['configValue']

                logger.info(f"Converted config dictionary with keys: {list(config_dict.keys())}")

                # Update settings with fetched config
                if config_dict.get("PIPELINE_AGENT_SERVICE_ENDPOINT"):
                    logger.info(f"Updating PIPELINE_AGENT_SERVICE_ENDPOINT: {config_dict.get('PIPELINE_AGENT_SERVICE_ENDPOINT')}")
                    self.pipeline_agent_service_endpoint = config_dict.get("PIPELINE_AGENT_SERVICE_ENDPOINT")

                if config_dict.get("INDIVIDUAL_AGENT_SERVICE_ENDPOINT"):
                    logger.info(f"Updating INDIVIDUAL_AGENT_SERVICE_ENDPOINT: {config_dict.get('INDIVIDUAL_AGENT_SERVICE_ENDPOINT')}")
                    self.individual_agent_service_endpoint = config_dict.get("INDIVIDUAL_AGENT_SERVICE_ENDPOINT")
                    self.conversational_agent_service_endpoint = config_dict.get("INDIVIDUAL_AGENT_SERVICE_ENDPOINT") + "/chat"

                if config_dict.get("REDIS_URL"):
                    logger.info("Updating REDIS_URL: [HIDDEN]")
                    self.redis_url = config_dict.get("REDIS_URL")

                if config_dict.get("ACCESS_KEY"):
                    logger.info("Updating ACCESS_KEY: [HIDDEN]")
                    self.access_key = config_dict.get("ACCESS_KEY")

                if config_dict.get("PLATFORM_BASE_URL"):
                    logger.info(f"Updating PLATFORM_BASE_URL: {config_dict.get('PLATFORM_BASE_URL')}")
                    self.base_url = config_dict.get("PLATFORM_BASE_URL")

                # Update database settings from config
                if config_dict.get("DB_HOST"):
                    logger.info(f"Updating DB_HOST: {config_dict.get('DB_HOST')}")
                    self.db_host = config_dict.get("DB_HOST")
                if config_dict.get("DB_PORT"):
                    logger.info(f"Updating DB_PORT: {config_dict.get('DB_PORT')}")
                    self.db_port = int(config_dict.get("DB_PORT"))
                if config_dict.get("DB_USER"):
                    logger.info(f"Updating DB_USER: {config_dict.get('DB_USER')}")
                    self.db_user = config_dict.get("DB_USER")
                if config_dict.get("DB_PASSWORD"):
                    logger.info("Updating DB_PASSWORD: [HIDDEN]")
                    self.db_password = config_dict.get("DB_PASSWORD")
                if config_dict.get("DB_NAME"):
                    logger.info(f"Updating DB_NAME: {config_dict.get('DB_NAME')}")
                    self.db_name = config_dict.get("DB_NAME")

                logger.info("All settings updated from API config")
            else:
                logger.warning("No config data received from API or invalid format")

            logger.info("Runtime configuration loaded successfully")

        except Exception as e:
            logger.error(f"Failed to fetch runtime config: {e}")
            # Fallback to environment variables


# Global settings instance
settings = Settings()
