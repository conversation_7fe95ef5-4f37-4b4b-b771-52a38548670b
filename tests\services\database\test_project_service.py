"""
Tests for ProjectService database operations.
"""

import pytest
import asyncpg
from uuid import UUID, uuid4
from src.services.database.project_service import ProjectService
from src.services.database.user_service import UserService


@pytest.mark.asyncio
class TestProjectService:
    """Test class for ProjectService."""

    @pytest.fixture
    async def project_service(self, test_db_pool: asyncpg.Pool):
        """Create ProjectService instance for testing."""
        return ProjectService(test_db_pool)

    @pytest.fixture
    async def user_service(self, test_db_pool: asyncpg.Pool):
        """Create UserService instance for testing."""
        return UserService(test_db_pool)

    @pytest.fixture
    async def test_user(self, user_service: UserService, clean_db):
        """Create a test user for project operations."""
        user = await user_service.create_user(
            email="<EMAIL>", full_name="Project Test User"
        )
        return UUID(user["user_id"])

    async def test_create_project(
        self, project_service: ProjectService, test_user: UUID, sample_project_data
    ):
        """Test creating a new project."""
        # Create project
        result = await project_service.create_project(
            name=sample_project_data["name"],
            description=sample_project_data["description"],
            created_by=test_user,
            run_id=sample_project_data["run_id"],
        )

        # Assertions
        assert result is not None
        assert "id" in result
        assert result["name"] == sample_project_data["name"]
        assert result["description"] == sample_project_data["description"]
        assert result["created_by"] == str(test_user)
        assert result["run_id"] == str(sample_project_data["run_id"])

    async def test_create_project_auto_run_id(
        self, project_service: ProjectService, test_user: UUID
    ):
        """Test creating a project with auto-generated run_id."""
        result = await project_service.create_project(
            name="Auto Run ID Project",
            description="Test project with auto run ID",
            created_by=test_user,
        )

        assert result is not None
        assert "run_id" in result
        # Verify run_id is a valid UUID string
        run_id = UUID(result["run_id"])
        assert isinstance(run_id, UUID)

    async def test_get_project_by_run_id(
        self, project_service: ProjectService, test_user: UUID, sample_project_data
    ):
        """Test retrieving project by run_id."""
        # Create project first
        created_project = await project_service.create_project(
            name=sample_project_data["name"],
            description=sample_project_data["description"],
            created_by=test_user,
            run_id=sample_project_data["run_id"],
        )

        # Get project by run_id
        result = await project_service.get_project_by_run_id(
            sample_project_data["run_id"]
        )

        # Assertions
        assert result is not None
        assert result["id"] == created_project["id"]
        assert result["name"] == sample_project_data["name"]
        assert result["run_id"] == sample_project_data["run_id"]

    async def test_get_project_by_run_id_not_found(
        self, project_service: ProjectService
    ):
        """Test retrieving non-existent project by run_id."""
        non_existent_run_id = uuid4()
        result = await project_service.get_project_by_run_id(non_existent_run_id)
        assert result is None

    async def test_update_market_research(
        self,
        project_service: ProjectService,
        test_user: UUID,
        sample_project_data,
        sample_market_research_data,
    ):
        """Test updating market research data."""
        # Create project first
        project = await project_service.create_project(
            name=sample_project_data["name"],
            description=sample_project_data["description"],
            created_by=test_user,
        )

        # Update market research
        mr_id = await project_service.update_market_research(
            project_id=project["id"],
            market_summary=sample_market_research_data["market_summary"],
            identified_gaps=sample_market_research_data["identified_gaps"],
            competitors=sample_market_research_data["competitors"],
        )

        # Assertions
        assert mr_id is not None
        assert isinstance(mr_id, int)

        # Verify data was stored correctly
        retrieved_data = await project_service.get_market_research(project["id"])
        assert retrieved_data is not None
        assert (
            retrieved_data["market_summary"]
            == sample_market_research_data["market_summary"]
        )
        assert (
            retrieved_data["identified_gaps"]
            == sample_market_research_data["identified_gaps"]
        )
        assert len(retrieved_data["competitors"]) == len(
            sample_market_research_data["competitors"]
        )

    async def test_get_market_research_not_found(self, project_service: ProjectService):
        """Test retrieving market research for non-existent project."""
        result = await project_service.get_market_research(99999)
        assert result is None

    async def test_update_lean_business_canvas(
        self,
        project_service: ProjectService,
        test_user: UUID,
        sample_project_data,
        sample_lbc_data,
    ):
        """Test updating lean business canvas data."""
        # Create project first
        project = await project_service.create_project(
            name=sample_project_data["name"],
            description=sample_project_data["description"],
            created_by=test_user,
        )

        # Update lean business canvas
        lbc_id = await project_service.update_lean_business_canvas(
            project_id=project["id"], lbc_data=sample_lbc_data
        )

        # Assertions
        assert lbc_id is not None
        assert isinstance(lbc_id, int)

        # Verify data was stored correctly
        retrieved_data = await project_service.get_lean_business_canvas(project["id"])
        assert retrieved_data is not None
        assert retrieved_data["problem"] == sample_lbc_data["problem"]
        assert retrieved_data["solution"] == sample_lbc_data["solution"]
        assert retrieved_data["key_partners"] == sample_lbc_data["key_partners"]

    async def test_get_lean_business_canvas_not_found(
        self, project_service: ProjectService
    ):
        """Test retrieving lean business canvas for non-existent project."""
        result = await project_service.get_lean_business_canvas(99999)
        assert result is None

    async def test_update_user_personas(
        self,
        project_service: ProjectService,
        test_user: UUID,
        sample_project_data,
        sample_personas_data,
    ):
        """Test updating user personas data."""
        # Create project first
        project = await project_service.create_project(
            name=sample_project_data["name"],
            description=sample_project_data["description"],
            created_by=test_user,
        )

        # Update user personas
        persona_ids = await project_service.update_user_personas(
            project_id=project["id"], personas=sample_personas_data
        )

        # Assertions
        assert persona_ids is not None
        assert len(persona_ids) == len(sample_personas_data)
        assert all(isinstance(pid, int) for pid in persona_ids)

        # Verify data was stored correctly
        retrieved_data = await project_service.get_user_personas(project["id"])
        assert retrieved_data is not None

        # Handle both single persona and multiple personas response format
        if "personas" in retrieved_data:
            personas_list = retrieved_data["personas"]
        else:
            personas_list = [retrieved_data]

        assert len(personas_list) == len(sample_personas_data)

    async def test_get_user_personas_not_found(self, project_service: ProjectService):
        """Test retrieving user personas for non-existent project."""
        result = await project_service.get_user_personas(99999)
        assert result is None

    async def test_update_user_personas_gender_normalization(
        self,
        project_service: ProjectService,
        test_user: UUID,
        sample_project_data,
    ):
        """Test that gender values are normalized to lowercase."""
        # Create project first
        project = await project_service.create_project(
            name=sample_project_data["name"],
            description=sample_project_data["description"],
            created_by=test_user,
        )

        # Test data with capitalized gender values (as they come from AI)
        personas_with_capitalized_gender = [
            {
                "name": "Test User Male",
                "role": "Developer",
                "age": 30,
                "gender": "Male",  # Capitalized
                "education": "Bachelor's",
                "status": "Single",
                "location": "Test City",
                "tech_literacy": "High",
                "avatar": None,
                "quote": "Test quote",
                "personality": ["Test"],
                "pain_points": ["Test pain"],
                "goals": ["Test goal"],
                "motivation": ["Test motivation"],
                "expectations": ["Test expectation"],
                "devices": ["laptop"],
            },
            {
                "name": "Test User Female",
                "role": "Designer",
                "age": 25,
                "gender": "Female",  # Capitalized
                "education": "Master's",
                "status": "Married",
                "location": "Test City 2",
                "tech_literacy": "Medium",
                "avatar": None,
                "quote": "Test quote 2",
                "personality": ["Creative"],
                "pain_points": ["Test pain 2"],
                "goals": ["Test goal 2"],
                "motivation": ["Test motivation 2"],
                "expectations": ["Test expectation 2"],
                "devices": ["tablet"],
            },
            {
                "name": "Test User Non-Binary",
                "role": "Manager",
                "age": 35,
                "gender": "Non-binary",  # Capitalized
                "education": "PhD",
                "status": "Single",
                "location": "Test City 3",
                "tech_literacy": "High",
                "avatar": None,
                "quote": "Test quote 3",
                "personality": ["Analytical"],
                "pain_points": ["Test pain 3"],
                "goals": ["Test goal 3"],
                "motivation": ["Test motivation 3"],
                "expectations": ["Test expectation 3"],
                "devices": ["mobile"],
            },
        ]

        # Update user personas - this should not raise a constraint violation
        persona_ids = await project_service.update_user_personas(
            project_id=project["id"], personas=personas_with_capitalized_gender
        )

        # Verify the personas were created successfully
        assert persona_ids is not None
        assert len(persona_ids) == 3
        assert all(isinstance(pid, int) for pid in persona_ids)

        # Verify that the gender values were normalized by checking the database directly
        async with project_service.db_pool.acquire() as conn:
            stored_personas = await conn.fetch(
                """
                SELECT name, gender FROM aava_prod_studio.user_personas
                WHERE project_id = $1 ORDER BY name
                """,
                project["id"],
            )

        # Check that all gender values are lowercase in the database
        assert len(stored_personas) == 3
        assert stored_personas[0]["gender"] == "male"  # "Male" -> "male"
        assert stored_personas[1]["gender"] == "female"  # "Female" -> "female"
        assert (
            stored_personas[2]["gender"] == "non-binary"
        )  # "Non-binary" -> "non-binary"

    async def test_update_swot_analysis(
        self,
        project_service: ProjectService,
        test_user: UUID,
        sample_project_data,
        sample_swot_data,
    ):
        """Test updating SWOT analysis data."""
        # Create project first
        project = await project_service.create_project(
            name=sample_project_data["name"],
            description=sample_project_data["description"],
            created_by=test_user,
        )

        # Update SWOT analysis
        swot_id = await project_service.update_swot_analysis(
            project_id=project["id"], swot_data=sample_swot_data
        )

        # Assertions
        assert swot_id is not None
        assert isinstance(swot_id, int)

        # Verify data was stored correctly
        retrieved_data = await project_service.get_swot_analysis(project["id"])
        assert retrieved_data is not None
        assert retrieved_data["strengths"] == sample_swot_data["strengths"]
        assert retrieved_data["weaknesses"] == sample_swot_data["weaknesses"]
        assert retrieved_data["opportunities"] == sample_swot_data["opportunities"]
        assert retrieved_data["threats"] == sample_swot_data["threats"]

    async def test_update_features(
        self,
        project_service: ProjectService,
        test_user: UUID,
        sample_project_data,
        sample_features_data,
    ):
        """Test updating features data."""
        # Create project first
        project = await project_service.create_project(
            name=sample_project_data["name"],
            description=sample_project_data["description"],
            created_by=test_user,
        )

        # Update features
        feature_ids = await project_service.update_features(
            project_id=project["id"], features=sample_features_data
        )

        # Assertions
        assert feature_ids is not None
        assert len(feature_ids) == len(sample_features_data)
        assert all(isinstance(fid, int) for fid in feature_ids)

        # Verify data was stored correctly
        retrieved_data = await project_service.get_features(project["id"])
        assert retrieved_data is not None
        assert len(retrieved_data) == len(sample_features_data)

    async def test_get_features_not_found(self, project_service: ProjectService):
        """Test retrieving features for non-existent project."""
        result = await project_service.get_features(99999)
        assert result is None

    async def test_update_roadmap_tasks(
        self,
        project_service: ProjectService,
        test_user: UUID,
        sample_project_data,
        sample_roadmap_tasks_data,
    ):
        """Test updating roadmap tasks data."""
        # Create project first
        project = await project_service.create_project(
            name=sample_project_data["name"],
            description=sample_project_data["description"],
            created_by=test_user,
        )

        # Update roadmap tasks
        task_ids = await project_service.update_roadmap_tasks(
            project_id=project["id"], tasks=sample_roadmap_tasks_data
        )

        # Assertions
        assert task_ids is not None
        assert len(task_ids) == len(sample_roadmap_tasks_data)
        assert all(isinstance(tid, int) for tid in task_ids)

        # Verify data was stored correctly
        retrieved_data = await project_service.get_roadmap_tasks(project["id"])
        assert retrieved_data is not None
        assert len(retrieved_data) == len(sample_roadmap_tasks_data)

    async def test_get_roadmap_tasks_not_found(self, project_service: ProjectService):
        """Test retrieving roadmap tasks for non-existent project."""
        result = await project_service.get_roadmap_tasks(99999)
        assert result is None

    async def test_add_conversation_message(
        self, project_service: ProjectService, test_user: UUID, sample_project_data
    ):
        """Test adding conversation messages."""
        # Create project first
        project = await project_service.create_project(
            name=sample_project_data["name"],
            description=sample_project_data["description"],
            created_by=test_user,
        )

        # Add conversation messages
        message_id_1 = await project_service.add_conversation_message(
            project_id=project["id"],
            conversation_type="market_research",
            content="Hello, let's discuss market research",
            role="user",
        )

        message_id_2 = await project_service.add_conversation_message(
            project_id=project["id"],
            conversation_type="market_research",
            content="Sure, I can help with that",
            role="assistant",
        )

        # Assertions
        assert message_id_1 is not None
        assert message_id_2 is not None
        assert isinstance(message_id_1, int)
        assert isinstance(message_id_2, int)

    async def test_get_conversation_messages(
        self, project_service: ProjectService, test_user: UUID, sample_project_data
    ):
        """Test retrieving conversation messages."""
        # Create project first
        project = await project_service.create_project(
            name=sample_project_data["name"],
            description=sample_project_data["description"],
            created_by=test_user,
        )

        # Add multiple conversation messages
        messages_data = [
            ("Hello", "user"),
            ("Hi there!", "assistant"),
            ("Can you help me?", "user"),
            ("Of course!", "assistant"),
        ]

        for content, role in messages_data:
            await project_service.add_conversation_message(
                project_id=project["id"],
                conversation_type="market_research",
                content=content,
                role=role,
            )

        # Retrieve messages
        retrieved_messages = await project_service.get_conversation_messages(
            project_id=project["id"], conversation_type="market_research"
        )

        # Assertions
        assert retrieved_messages is not None
        assert len(retrieved_messages) == len(messages_data)

        # Check message order and content
        for i, message in enumerate(retrieved_messages):
            assert message["content"] == messages_data[i][0]
            assert message["role"] == messages_data[i][1]
            assert message["message_order"] == i + 1
            assert "created_at" in message

    async def test_get_conversation_messages_empty(
        self, project_service: ProjectService, test_user: UUID, sample_project_data
    ):
        """Test retrieving conversation messages when none exist."""
        # Create project first
        project = await project_service.create_project(
            name=sample_project_data["name"],
            description=sample_project_data["description"],
            created_by=test_user,
        )

        # Retrieve messages for empty conversation
        retrieved_messages = await project_service.get_conversation_messages(
            project_id=project["id"], conversation_type="market_research"
        )

        # Assertions
        assert retrieved_messages == []

    async def test_conversation_message_ordering(
        self, project_service: ProjectService, test_user: UUID, sample_project_data
    ):
        """Test that conversation messages are ordered correctly."""
        # Create project first
        project = await project_service.create_project(
            name=sample_project_data["name"],
            description=sample_project_data["description"],
            created_by=test_user,
        )

        # Add messages in specific order
        await project_service.add_conversation_message(
            project_id=project["id"],
            conversation_type="lbc",
            content="First message",
            role="user",
        )

        await project_service.add_conversation_message(
            project_id=project["id"],
            conversation_type="lbc",
            content="Second message",
            role="assistant",
        )

        await project_service.add_conversation_message(
            project_id=project["id"],
            conversation_type="lbc",
            content="Third message",
            role="user",
        )

        # Retrieve messages
        messages = await project_service.get_conversation_messages(
            project_id=project["id"], conversation_type="lbc"
        )

        # Verify ordering
        assert len(messages) == 3
        assert messages[0]["content"] == "First message"
        assert messages[0]["message_order"] == 1
        assert messages[1]["content"] == "Second message"
        assert messages[1]["message_order"] == 2
        assert messages[2]["content"] == "Third message"
        assert messages[2]["message_order"] == 3
