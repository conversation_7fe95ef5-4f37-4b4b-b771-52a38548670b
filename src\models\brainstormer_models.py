from pydantic import BaseModel, Field
from typing import List, Dict, Any, Literal, Optional


# Step 0: Market Research
class Competitor(BaseModel):
    name: str
    url: str
    strengths: str


class MarketResearch(BaseModel):
    market_summary: str
    competitors: List[Competitor]
    identified_gaps: str


# Step 1: Lean Business Canvas
class LeanBusinessCanvas(BaseModel):
    problem: List[str] = Field(default_factory=list)
    solution: List[str] = Field(default_factory=list)
    key_partners: Optional[List[str]] = Field(
        default_factory=list, alias="key_partners"
    )
    value_proposition: List[str] = Field(
        default_factory=list, alias="value_proposition"
    )
    customer_segments: List[str] = Field(
        default_factory=list, alias="customer_segments"
    )
    revenue_streams: Optional[List[str]] = Field(
        default_factory=list, alias="revenue_streams"
    )
    key_metrics: List[str] = Field(default_factory=list, alias="key_metrics")
    alternatives: Optional[List[str]] = Field(default_factory=list)
    solution_tenants: List[str] = Field(default_factory=list)

    class Config:
        allow_population_by_field_name = True


# Step 2: User Persona
class Skill(BaseModel):
    name: str
    level: int = Field(ge=0, le=100)


class UserPersona(BaseModel):
    name: str
    role: str
    age: int
    education: str
    gender: str
    status: str
    location: str
    tech_literacy: str = Field(alias="techLiteracy")
    avatar: Optional[str] = None
    quote: str
    personality: List[str] = Field(default_factory=list)
    pain_points: List[str] = Field(default_factory=list, alias="painPoints")
    goals: List[str] = Field(default_factory=list)
    motivation: List[str] = Field(default_factory=list)
    expectations: List[str] = Field(default_factory=list)
    skills: List[Skill] = Field(default_factory=list)
    devices: List[str] = Field(default_factory=list)

    class Config:
        allow_population_by_field_name = True


class PersonaCollection(BaseModel):
    personas: List[UserPersona] = Field(default_factory=list)


# Step 3 & 4: SWOT and Features
class SwotItem(BaseModel):
    title: str
    description: str = ""
    justification: str = ""
    tags: List[str] = Field(default_factory=list)
    impact: int = Field(ge=0, le=100)  # Impact score 0-100
    priority: int = Field(ge=0, le=100)  # Priority score 0-100


class SwotAnalysis(BaseModel):
    strengths: List[SwotItem] = Field(default_factory=list)
    weaknesses: List[SwotItem] = Field(default_factory=list)
    opportunities: List[SwotItem] = Field(default_factory=list)
    threats: List[SwotItem] = Field(default_factory=list)


class Feature(BaseModel):
    title: str  # Changed from 'name' to 'title' to match frontend
    description: str
    tenant: str
    justification: str
    tags: List[str] = Field(default_factory=list)
    moscow_rank: Literal["Must-Have", "Should-Have", "Could-Have", "Won't-Have"]


class FeaturesByCategory(BaseModel):
    must_have: List[Feature] = Field(default_factory=list, alias="must_have")
    should_have: List[Feature] = Field(default_factory=list, alias="should_have")
    could_have: List[Feature] = Field(default_factory=list, alias="could_have")
    wont_have: List[Feature] = Field(default_factory=list, alias="wont_have")

    class Config:
        allow_population_by_field_name = True


# Step 5: Roadmap
class RoadmapTask(BaseModel):
    task: str
    description: str
    long_description: str
    priority: Literal["low", "medium", "high"]
    duration: int  # Duration in days/weeks (you can specify the unit)
    quarter: int = Field(ge=1, le=4)


class Roadmap(BaseModel):
    project_tasks: List[RoadmapTask] = Field(default_factory=list, alias="projectTasks")

    class Config:
        allow_population_by_field_name = True


# Conversational Agent Response Models
class EditPayload(BaseModel):
    # This will hold the full JSON of the updated artifact,
    # which can be any of our step models (e.g., LeanBusinessCanvas)
    payload: Dict[str, Any]


class DelegateSearchPayload(BaseModel):
    query: str


class ClarificationPayload(BaseModel):
    pass  # No payload needed


class ConversationalResponse(BaseModel):
    response_type: Literal["edit", "delegate_search", "clarification"]
    message_to_user: str
    payload: Dict[str, Any]


# Pipeline Data Management Models
class PipelineDataRequest(BaseModel):
    run_id: str
    step: str
    operation_type: Literal["update", "delete", "add"]
    payload: Dict[str, Any]


class PipelineDataResponse(BaseModel):
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None


# Regeneration Models
class RegenerateRequest(BaseModel):
    run_id: str
    step: str  # e.g., "lbc", "persona", "swot", etc.
    user_request: str  # User's specific request for regeneration
    additional_context: Optional[Dict[str, Any]] = None  # Any additional context needed


class RegenerateResponse(BaseModel):
    success: bool
    message: str
    updated_fields: Optional[Dict[str, Any]] = (
        None  # The complete data with regenerated field(s)
    )
