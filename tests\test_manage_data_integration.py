#!/usr/bin/env python3
"""
Integration test script for the /pipeline/manage-data endpoint.
This script tests the endpoint with real HTTP requests.
"""

import asyncio
import sys
import os
from typing import Dict, Any

# Add the project root to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), ".."))

import httpx


class ManageDataIntegrationTest:
    """Integration test class for the manage data endpoint."""

    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.test_run_id = "test-integration-run-123"
        self.test_headers = {
            "access-key": "Bearer test-jwt-token",  # Mock JWT token
            "Content-Type": "application/json",
        }

    async def test_endpoint_availability(self) -> bool:
        """Test if the endpoint is available."""
        async with httpx.AsyncClient() as client:
            try:
                response = await client.get(f"{self.base_url}/heartbeat")
                return response.status_code == 200
            except Exception as e:
                print(f"❌ Server not available: {e}")
                return False

    async def test_update_operation(self) -> Dict[str, Any]:
        """Test the update operation."""
        request_data = {
            "run_id": self.test_run_id,
            "step": "market_research",
            "operation_type": "update",
            "payload": {
                "market_summary": "Updated market summary for integration test",
                "identified_gaps": "Updated gaps identified during testing",
            },
        }

        async with httpx.AsyncClient() as client:
            try:
                response = await client.post(
                    f"{self.base_url}/api/v1/pipeline/manage-data",
                    json=request_data,
                    headers=self.test_headers,
                    timeout=30.0,
                )

                return {
                    "status_code": response.status_code,
                    "response": response.json()
                    if response.status_code != 500
                    else {"error": response.text},
                    "operation": "update",
                }
            except Exception as e:
                return {
                    "status_code": 0,
                    "response": {"error": str(e)},
                    "operation": "update",
                }

    async def test_add_operation(self) -> Dict[str, Any]:
        """Test the add operation."""
        request_data = {
            "run_id": self.test_run_id,
            "step": "market_research",
            "operation_type": "add",
            "payload": {
                "competitors": [
                    {
                        "name": "Integration Test Competitor",
                        "url": "https://integration-test.com",
                        "strengths": "Used for testing purposes",
                    }
                ]
            },
        }

        async with httpx.AsyncClient() as client:
            try:
                response = await client.post(
                    f"{self.base_url}/api/v1/pipeline/manage-data",
                    json=request_data,
                    headers=self.test_headers,
                    timeout=30.0,
                )

                return {
                    "status_code": response.status_code,
                    "response": response.json()
                    if response.status_code != 500
                    else {"error": response.text},
                    "operation": "add",
                }
            except Exception as e:
                return {
                    "status_code": 0,
                    "response": {"error": str(e)},
                    "operation": "add",
                }

    async def test_delete_operation(self) -> Dict[str, Any]:
        """Test the delete operation."""
        request_data = {
            "run_id": self.test_run_id,
            "step": "market_research",
            "operation_type": "delete",
            "payload": {
                "competitors": [
                    {
                        "name": "Integration Test Competitor",
                        "url": "https://integration-test.com",
                        "strengths": "Used for testing purposes",
                    }
                ]
            },
        }

        async with httpx.AsyncClient() as client:
            try:
                response = await client.post(
                    f"{self.base_url}/api/v1/pipeline/manage-data",
                    json=request_data,
                    headers=self.test_headers,
                    timeout=30.0,
                )

                return {
                    "status_code": response.status_code,
                    "response": response.json()
                    if response.status_code != 500
                    else {"error": response.text},
                    "operation": "delete",
                }
            except Exception as e:
                return {
                    "status_code": 0,
                    "response": {"error": str(e)},
                    "operation": "delete",
                }

    async def test_invalid_step(self) -> Dict[str, Any]:
        """Test with invalid step name."""
        request_data = {
            "run_id": self.test_run_id,
            "step": "invalid_step",
            "operation_type": "update",
            "payload": {"test": "data"},
        }

        async with httpx.AsyncClient() as client:
            try:
                response = await client.post(
                    f"{self.base_url}/api/v1/pipeline/manage-data",
                    json=request_data,
                    headers=self.test_headers,
                    timeout=30.0,
                )

                return {
                    "status_code": response.status_code,
                    "response": response.json()
                    if response.status_code != 500
                    else {"error": response.text},
                    "operation": "invalid_step",
                }
            except Exception as e:
                return {
                    "status_code": 0,
                    "response": {"error": str(e)},
                    "operation": "invalid_step",
                }

    async def test_invalid_operation_type(self) -> Dict[str, Any]:
        """Test with invalid operation type."""
        request_data = {
            "run_id": self.test_run_id,
            "step": "market_research",
            "operation_type": "invalid_operation",
            "payload": {"test": "data"},
        }

        async with httpx.AsyncClient() as client:
            try:
                response = await client.post(
                    f"{self.base_url}/api/v1/pipeline/manage-data",
                    json=request_data,
                    headers=self.test_headers,
                    timeout=30.0,
                )

                return {
                    "status_code": response.status_code,
                    "response": response.json()
                    if response.status_code != 500
                    else {"error": response.text},
                    "operation": "invalid_operation_type",
                }
            except Exception as e:
                return {
                    "status_code": 0,
                    "response": {"error": str(e)},
                    "operation": "invalid_operation_type",
                }

    async def run_all_tests(self):
        """Run all integration tests."""
        print("🚀 Starting /pipeline/manage-data Integration Tests")
        print("=" * 60)

        # Check if server is available
        if not await self.test_endpoint_availability():
            print("❌ Server is not available. Please start the server first.")
            return

        print("✅ Server is available")
        print()

        # Run all test cases
        test_cases = [
            ("Update Operation", self.test_update_operation),
            ("Add Operation", self.test_add_operation),
            ("Delete Operation", self.test_delete_operation),
            ("Invalid Step", self.test_invalid_step),
            ("Invalid Operation Type", self.test_invalid_operation_type),
        ]

        results = []
        for test_name, test_func in test_cases:
            print(f"🧪 Running: {test_name}")
            result = await test_func()
            results.append((test_name, result))

            # Print result
            status_code = result["status_code"]
            # operation = result["operation"]

            if status_code == 200:
                print(f"✅ {test_name}: SUCCESS (200)")
                if result["response"].get("success"):
                    print(f"   Message: {result['response'].get('message', 'N/A')}")
                else:
                    print(
                        f"   Operation failed: {result['response'].get('message', 'N/A')}"
                    )
            elif status_code == 400:
                print(
                    f"⚠️  {test_name}: BAD REQUEST (400) - Expected for invalid inputs"
                )
                print(f"   Error: {result['response'].get('detail', 'N/A')}")
            elif status_code == 422:
                print(f"⚠️  {test_name}: VALIDATION ERROR (422)")
                print(f"   Error: {result['response'].get('detail', 'N/A')}")
            elif status_code == 500:
                print(f"❌ {test_name}: SERVER ERROR (500)")
                print(f"   Error: {result['response'].get('error', 'N/A')}")
            else:
                print(f"❌ {test_name}: UNEXPECTED STATUS ({status_code})")
                print(f"   Response: {result['response']}")

            print()

        # Summary
        print("=" * 60)
        print("📊 Test Summary:")
        successful_tests = sum(
            1 for _, result in results if result["status_code"] in [200, 400]
        )
        total_tests = len(results)
        print(f"   Total Tests: {total_tests}")
        print(f"   Successful: {successful_tests}")
        print(f"   Failed: {total_tests - successful_tests}")

        if successful_tests == total_tests:
            print("🎉 All tests completed successfully!")
        else:
            print("⚠️  Some tests failed. Check the output above for details.")


async def main():
    """Main function to run the integration tests."""
    import argparse

    parser = argparse.ArgumentParser(
        description="Integration tests for /pipeline/manage-data endpoint"
    )
    parser.add_argument(
        "--url", default="http://localhost:8000", help="Base URL of the API server"
    )
    args = parser.parse_args()

    tester = ManageDataIntegrationTest(base_url=args.url)
    await tester.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())
