# Regeneration API Documentation

## Overview

The Regeneration API allows users to regenerate specific portions of data while maintaining the overall structure and context. This is particularly useful when users want to edit specific fields in their brainstormer data (like problems in LBC, pain points in personas, etc.) without regenerating the entire dataset.

## API Endpoint

**POST** `/brainstormer/regenerate`

## Authentication

Requires <PERSON><PERSON><PERSON> token in the `access-key` header.

## Request Model

```python
class RegenerateRequest(BaseModel):
    run_id: str                              # The pipeline run ID
    data_type: str                           # Type of data (e.g., "lbc", "persona", "swot")
    field_to_regenerate: str                 # Specific field to regenerate
    current_data: Dict[str, Any]             # Complete current data structure
    user_request: str                        # User's specific regeneration request
    additional_context: Optional[Dict[str, Any]] = None  # Extra context if needed
```

## Response Model

```python
class RegenerateResponse(BaseModel):
    success: bool                            # Whether the operation succeeded
    message: str                             # Status message
    regenerated_data: Optional[Dict[str, Any]] = None  # Complete data with regenerated field(s)
```

## Supported Data Types and Fields

### 1. Lean Business Canvas (lbc)
- `problem` - List of problems the business solves
- `solution` - List of solutions offered
- `key_partners` - Key business partners
- `value_proposition` - Value propositions
- `customer_segments` - Target customer segments
- `revenue_streams` - Revenue generation methods
- `key_metrics` - Key performance indicators
- `alternatives` - Alternative solutions in market
- `solution_tenants` - Different parts of the application

### 2. User Persona (persona)
- `painPoints` - User pain points
- `goals` - User goals
- `motivation` - User motivations
- `expectations` - User expectations
- `personality` - Personality traits
- `skills` - User skills and proficiency levels
- `devices` - Devices used by the persona

### 3. SWOT Analysis (swot)
- `strengths` - Business strengths
- `weaknesses` - Business weaknesses
- `opportunities` - Market opportunities
- `threats` - Potential threats

### 4. Features (features)
- `must_have` - Must-have features
- `should_have` - Should-have features
- `could_have` - Could-have features
- `wont_have` - Won't-have features

## Usage Examples

### Example 1: Regenerate LBC Problems

```json
{
  "run_id": "abc-123-def",
  "data_type": "lbc",
  "field_to_regenerate": "problem",
  "current_data": {
    "problem": [
      "Users struggle to find reliable local services",
      "Lack of transparency in service provider ratings"
    ],
    "solution": ["Platform connecting users with verified providers"],
    // ... rest of LBC data
  },
  "user_request": "Make the problems more specific to small business owners needing accounting services",
  "additional_context": {
    "target_audience": "small business owners",
    "service_type": "accounting"
  }
}
```

### Example 2: Regenerate Persona Pain Points

```json
{
  "run_id": "abc-123-def",
  "data_type": "persona",
  "field_to_regenerate": "painPoints",
  "current_data": {
    "personas": [
      {
        "name": "Busy Professional",
        "painPoints": [
          "Limited time to research service providers",
          "Uncertainty about service quality"
        ],
        // ... rest of persona data
      }
    ]
  },
  "user_request": "Focus pain points on healthcare appointment booking challenges",
  "additional_context": {
    "industry": "healthcare",
    "focus": "appointment booking"
  }
}
```

### Example 3: Regenerate Multiple Fields

```json
{
  "run_id": "abc-123-def",
  "data_type": "lbc",
  "field_to_regenerate": "problem,solution",
  "current_data": {
    // ... complete LBC data
  },
  "user_request": "Pivot to focus on eco-friendly home services",
  "additional_context": {
    "focus": "sustainability",
    "market": "eco-conscious consumers"
  }
}
```

## Response Examples

### Success Response

```json
{
  "success": true,
  "message": "Successfully regenerated painPoints for persona",
  "regenerated_data": {
    "personas": [
      {
        "name": "Busy Professional",
        "painPoints": [
          "Difficulty finding available appointment slots",
          "Long waiting times for healthcare services",
          "Complex booking systems"
        ],
        // ... rest of persona data unchanged
      }
    ]
  }
}
```

### Error Response

```json
{
  "success": false,
  "message": "Validation error: Invalid data_type specified",
  "regenerated_data": null
}
```

## Implementation Details

### Backend Components

1. **Prompt Template** (`src/config/brainstormer_prompt.py`)
   - `REGENERATE_PROMPT` - Template for regeneration requests

2. **Service Method** (`src/services/aava_service.py`)
   - `regenerate_data_portion()` - Core regeneration logic in both `AAVAAIService` and `ConversationalAgentService`

3. **API Endpoint** (`src/routes/brainstormer_route.py`)
   - `/regenerate` - REST endpoint for regeneration requests

4. **Data Models** (`src/models/brainstormer_models.py`)
   - `RegenerateRequest` - Request model
   - `RegenerateResponse` - Response model

### Key Features

- **Context Preservation**: Maintains all existing data while regenerating specific fields
- **Flexible Field Selection**: Supports single or multiple field regeneration
- **Contextual Generation**: Uses additional context to guide regeneration
- **Error Handling**: Comprehensive error handling with meaningful messages
- **Validation**: Ensures regenerated data maintains proper structure

## Best Practices

1. **Specific Requests**: Provide clear, specific regeneration requests
2. **Context Information**: Include relevant additional context for better results
3. **Field Naming**: Use exact field names as defined in the data models
4. **Complete Data**: Always provide the complete current data structure
5. **Error Handling**: Handle both success and error responses appropriately

## Testing

Use the provided test script `test_regeneration_api.py` to generate sample requests and test the API functionality.
