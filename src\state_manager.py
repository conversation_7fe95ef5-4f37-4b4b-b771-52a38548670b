import redis.asyncio as redis
import json
from typing import Dict, Any
from src.utils.logger import AppLogger

logger = AppLogger("RedisManager").get_logger()


class RedisManager:
    def __init__(self, host="localhost", port=6379, db=0, url=None):
        self.connected = False
        try:
            if url:
                self.client = redis.from_url(url, decode_responses=True)
            else:
                self.client = redis.Redis(
                    host=host, port=port, db=db, decode_responses=True
                )
            self.connected = True
            logger.info(
                f"✅ Redis client initialized with {'URL' if url else f'{host}:{port}'}"
            )
        except Exception as e:
            logger.error(f"❌ Failed to initialize Redis client: {e}")
            self.client = None
            self.connected = False

    def _get_key(self, run_id: str, step_name: str) -> str:
        """Generates a consistent key for Redis."""
        return f"run:{run_id}:step:{step_name}"

    async def save_step_data(self, run_id: str, step_name: str, data: Dict[str, Any]):
        """Saves the JSON data for a pipeline step."""
        if not self.connected or not self.client:
            logger.warning("Redis not available - skipping save operation")
            return

        try:
            key = self._get_key(run_id, step_name)
            await self.client.set(key, json.dumps(data))
            logger.debug(f"Data saved for run_id '{run_id}', step '{step_name}'")
        except Exception as e:
            logger.error(
                f"Redis error saving data for run_id '{run_id}', step '{step_name}': {e}"
            )
            self.connected = False

    async def load_step_data(
        self, run_id: str, step_name: str
    ) -> Dict[str, Any] | None:
        """Loads the JSON data for a pipeline step."""
        if not self.connected or not self.client:
            logger.warning("Redis not available - cannot load data")
            return None

        try:
            key = self._get_key(run_id, step_name)
            data = await self.client.get(key)
            if data:
                logger.debug(f"Data loaded for run_id '{run_id}', step '{step_name}'")
                return json.loads(data)
            logger.debug(f"No data found for run_id '{run_id}', step '{step_name}'")
            return None
        except Exception as e:
            logger.error(
                f"Redis error loading data for run_id '{run_id}', step '{step_name}': {e}"
            )
            self.connected = False
            return None
