import asyncpg
import os
from contextlib import asynccontextmanager
from datetime import datetime, timezone
from fastapi import Depends, <PERSON><PERSON><PERSON>, Header, Request
from fastapi.middleware.cors import CORSMiddleware
from src.security.auth_helper import get_authenticated_user
from src.config.settings import settings
from src.routes.brainstormer_route import brainstormer_router
from src.security.jwt_auth_middleware import JWTAuthMiddleware
from src.utils.db_connection_manager import DatabaseConnectionManager
from src.security.user_details_decryptor import initialize_decryptor
import os
# Global variables to hold the database pool and connection manager
db_pool = None
db_manager = None


# Removed AccessKeyMiddleware - now using JWTAuthMiddleware


@asynccontextmanager
async def lifespan(app: FastAPI):
    global db_pool
    # Startup: fetch runtime configuration
    await settings.fetch_runtime_config()

    # Create database pool with optimized settings for connection management
    try:
        db_pool = await asyncpg.create_pool(
            host=settings.db_host,
            port=settings.db_port,
            user=settings.db_user,
            password=settings.db_password,
            database=settings.db_name,
            min_size=2,  # Reduced minimum connections
            max_size=10,  # Reduced maximum connections to prevent exhaustion
            max_queries=50000,  # Maximum queries per connection before recycling
            max_inactive_connection_lifetime=300.0,  # 5 minutes
            timeout=30.0,  # Connection acquisition timeout
            command_timeout=60.0,  # Command execution timeout
            server_settings={
                "application_name": "aava-prodstudio-api",
                "tcp_keepalives_idle": "600",
                "tcp_keepalives_interval": "30",
                "tcp_keepalives_count": "3",
            },
        )
        # Create connection manager
        global db_manager
        db_manager = DatabaseConnectionManager(db_pool)

        # Make db_pool and db_manager available to routes
        app.state.db_pool = db_pool
        app.state.db_manager = db_manager
        print(f"✅ Database connection pool established to {settings.db_host}")
        print(f"   Pool config: min={db_pool._minsize}, max={db_pool._maxsize}")
    except Exception as e:
        print(f"❌ Failed to connect to database: {e}")
        # Set db_pool to None so routes can handle gracefully
        app.state.db_pool = None

    yield

    # Shutdown: cleanup
    if db_manager:
        # Clean up any remaining connections
        await db_manager.cleanup_idle_connections()

    if db_pool:
        await db_pool.close()
        print("🔌 Database connection closed")


app = FastAPI(
    title="AI Product Roadmap Pipeline Orchestrator",
    lifespan=lifespan,
    root_path=os.getenv("ROOT_PATH", ""),
    docs_url="/server/product/docs",  # Custom docs URL
    redoc_url="/server/product/redoc",  # Optional: also customize ReDoc URL
    openapi_url="/server/product/openapi.json",  # Custom OpenAPI URL to match docs path
)


app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:4202",
        "https://aava-dev.avateam.io",
        "http://localhost:4200",
    ],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS", "HEAD", "PATCH"],
    allow_headers=[
        "access-key",
        "content-type",
        "authorization",
        "accept",
        "accept-encoding",
        "accept-language",
        "cache-control",
        "connection",
        "host",
        "origin",
        "pragma",
        "referer",
        "sec-fetch-dest",
        "sec-fetch-mode",
        "sec-fetch-site",
        "user-agent",
        "x-requested-with",
    ],
    expose_headers=["access-key","authorization"],
    max_age=3600,
)

# Add JWT authentication middleware AFTER CORS
app.add_middleware(JWTAuthMiddleware)

# Include routers
app.include_router(brainstormer_router, prefix="/server/product/api/v1")


@app.get("/")
async def root():
    return "Welcome to the FastAPI application!"


@app.get("/server/product/heartbeat")
async def heartbeat():
    return {"status": "ok"}


@app.get("/server/product/health")
async def health_check():
    """Comprehensive health check including database and Redis connectivity"""
    health_status = {
        "status": "healthy",
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "services": {
            "database": {"status": "unknown", "message": ""},
            "redis": {"status": "unknown", "message": ""},
            "api": {"status": "healthy", "message": "API is running"},
        },
    }

    # Check database connection using the connection manager
    if db_manager:
        db_health = await db_manager.health_check()
        health_status["services"]["database"] = db_health

        if db_health["status"] != "healthy":
            health_status["status"] = "degraded"
    else:
        health_status["services"]["database"] = {
            "status": "unavailable",
            "message": "Database not configured",
        }
        health_status["status"] = "degraded"

    # Check Redis connection (we'll need to import RedisManager)
    from src.state_manager import RedisManager

    try:
        redis_manager = RedisManager(url=settings.redis_url)
        if redis_manager.connected and redis_manager.client:
            await redis_manager.client.ping()
            health_status["services"]["redis"] = {
                "status": "healthy",
                "message": "Redis connection successful",
            }
        else:
            health_status["services"]["redis"] = {
                "status": "unavailable",
                "message": "Redis not configured",
            }
            if health_status["status"] == "healthy":
                health_status["status"] = "degraded"
    except Exception as e:
        health_status["services"]["redis"] = {
            "status": "unhealthy",
            "message": f"Redis connection failed: {str(e)}",
        }
        if health_status["status"] == "healthy":
            health_status["status"] = "degraded"

    return health_status


@app.get("/server/product/db-stats")
async def database_statistics(
    req: Request,
    user: dict = Depends(get_authenticated_user),
    access_key: str = Header(
        ..., alias="access-key", description="JWT token for authentication"
    ),
):
    """Get detailed database connection statistics"""
    if not db_manager:
        return {"error": "Database manager not available"}

    try:
        stats = db_manager.get_connection_stats()
        return {"timestamp": datetime.now(timezone.utc).isoformat(), **stats}
    except Exception as e:
        return {
            "error": f"Failed to get database statistics: {str(e)}",
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }


@app.post("/server/product/db-cleanup")
async def cleanup_database_connections(
    req: Request,
    user: dict = Depends(get_authenticated_user),
    access_key: str = Header(
        ..., alias="access-key", description="JWT token for authentication"
    ),
):
    """Force cleanup of idle database connections"""
    if not db_manager:
        return {"error": "Database manager not available"}

    try:
        await db_manager.cleanup_idle_connections()
        stats = db_manager.get_connection_stats()
        return {
            "message": "Database connection cleanup completed",
            "timestamp": datetime.now(timezone.utc).isoformat(),
            **stats,
        }
    except Exception as e:
        return {
            "error": f"Failed to cleanup database connections: {str(e)}",
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }
