# Docker Deployment Guide

This guide explains how to deploy the Product Studio API service using Docker.

## Quick Start

### Option 1: Docker Compose (Recommended)

1. **Copy environment file:**
   ```bash
   cp .env.docker .env
   ```

2. **Edit environment variables** in `.env` as needed for your deployment.

3. **Start the services:**
   ```bash
   docker-compose up -d
   ```

4. **Check health:**
   ```bash
   curl http://localhost:8000/health
   ```

### Option 2: Docker Build and Run

1. **Build the image:**
   ```bash
   docker build -t product-studio-api .
   ```

2. **Run the container:**
   ```bash
   docker run -p 8000:8000 \
     -e DB_HOST=your-db-host \
     -e DB_PASSWORD=your-db-password \
     -e REDIS_URL=redis://your-redis-host:6379/0 \
     product-studio-api
   ```

## Environment Variables

### Required for Production

- `DB_HOST` - Database hostname
- `DB_PASSWORD` - Database password
- `REDIS_URL` - Redis connection URL
- `ACCESS_KEY` - API access key

### Optional Configuration

- `DB_PORT` - Database port (default: 5432)
- `DB_USER` - Database username (default: postgres)
- `DB_NAME` - Database name (default: postgres)
- `BASE_URL` - API base URL (default: https://avaplus-internal.avateam.io)
- `DEBUG` - Enable debug mode (default: false)
- `ROOT_PATH` - Path prefix for reverse proxy/ingress (e.g., `/server/product`)

## Health Checks

The application provides several health check endpoints:

- `/heartbeat` - Simple health check
- `/health` - Comprehensive health check including database and Redis connectivity

Example health check response:
```json
{
  "status": "healthy",
  "timestamp": "2025-07-23T07:00:33.160626+00:00",
  "services": {
    "database": {
      "status": "healthy",
      "message": "Connected to your-db-host"
    },
    "redis": {
      "status": "healthy",
      "message": "Redis connection successful"
    },
    "api": {
      "status": "healthy",
      "message": "API is running"
    }
  }
}
```

## Error Handling

The application now gracefully handles:

- Database connection failures (runs without persistence)
- Redis connection failures (runs without caching)
- Service initialization errors
- Network connectivity issues

## Troubleshooting

### 500 Errors Fixed

The following issues that caused 500 errors have been resolved:

1. **Async configuration not awaited** - Fixed in `src/main.py`
2. **Database connection failures** - Added error handling and graceful fallbacks
3. **Redis connection failures** - Added error handling and graceful fallbacks
4. **Service initialization timing** - Services now initialize after configuration loading

### Common Issues

1. **Port already in use:**
   ```bash
   docker run -p 8001:8000 product-studio-api  # Use different port
   ```

2. **Database connection failed:**
   - Check `DB_HOST`, `DB_PASSWORD`, and network connectivity
   - Application will run without persistence if database is unavailable

3. **Redis connection failed:**
   - Check `REDIS_URL` and network connectivity
   - Application will run without caching if Redis is unavailable

## Kubernetes Deployment

For Kubernetes deployment with path prefix (e.g., `/server/product`):

### 1. Configure Root Path

Set the `ROOT_PATH` environment variable:
```bash
export ROOT_PATH="/server/product"
```

Or in your Kubernetes deployment:
```yaml
env:
- name: ROOT_PATH
  value: "/server/product"
```

### 2. Deploy to Kubernetes

1. **Update the secrets** in `k8s-deployment.yaml` with your actual values
2. **Apply the deployment:**
   ```bash
   kubectl apply -f k8s-deployment.yaml
   ```

3. **Verify deployment:**
   ```bash
   kubectl get pods -l app=product-studio-api
   kubectl logs -l app=product-studio-api
   ```

4. **Test the endpoints:**
   ```bash
   curl https://your-domain.com/server/product/heartbeat
   curl https://your-domain.com/server/product/health
   ```

### 3. Key Kubernetes Features

- **Health checks** configured for `/server/product/heartbeat` and `/server/product/health`
- **Ingress** with path rewriting for the `/server/product` prefix
- **Secrets** management for sensitive configuration
- **Resource limits** and requests configured
- **Multiple replicas** for high availability

## Production Deployment

For production deployment:

1. Use environment variables instead of hardcoded values
2. Set up proper database and Redis instances
3. Configure proper logging and monitoring
4. Use a reverse proxy (nginx) for SSL termination
5. Set up health check monitoring
6. Configure `ROOT_PATH` for path-based routing

Example production docker-compose.yml:
```yaml
version: '3.8'
services:
  app:
    image: product-studio-api:latest
    environment:
      - DB_HOST=${DB_HOST}
      - DB_PASSWORD=${DB_PASSWORD}
      - REDIS_URL=${REDIS_URL}
      - ACCESS_KEY=${ACCESS_KEY}
      - ROOT_PATH=${ROOT_PATH}
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000${ROOT_PATH}/heartbeat"]
      interval: 30s
      timeout: 10s
      retries: 3
```
