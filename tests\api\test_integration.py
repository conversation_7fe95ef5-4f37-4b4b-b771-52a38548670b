"""
Integration tests for API endpoints - testing complete workflows.
"""

import pytest
from unittest.mock import patch, AsyncMock, MagicMock
from httpx import AsyncClient
from fastapi import status


@pytest.mark.asyncio
class TestAPIIntegration:
    """Integration tests for complete API workflows."""

    @patch("src.routes.brainstormer_route.get_pipeline_manager")
    async def test_complete_pipeline_workflow(
        self, mock_get_pipeline_manager, test_client: AsyncClient, api_test_data
    ):
        """Test complete pipeline workflow from start to finish."""
        # Setup mock pipeline manager
        mock_pipeline_manager = AsyncMock()
        mock_get_pipeline_manager.return_value = mock_pipeline_manager

        # Mock responses for each step
        step_responses = {
            "market_research": {
                "market_summary": "Task management market analysis...",
                "identified_gaps": "Real-time collaboration gaps...",
                "competitors": [
                    {
                        "name": "Asana",
                        "url": "https://asana.com",
                        "strengths": "Project management",
                    }
                ],
            },
            "lbc": {
                "problem": ["Remote team coordination", "Task visibility"],
                "solution": ["Real-time dashboard", "Automated notifications"],
                "value_proposition": [
                    "Seamless collaboration",
                    "Increased productivity",
                ],
            },
            "persona": {
                "personas": [
                    {
                        "name": "<PERSON>",
                        "role": "Project Manager",
                        "age": 32,
                        "goals": [
                            "Improve team efficiency",
                            "Better project visibility",
                        ],
                    }
                ]
            },
            "swot": {
                "strengths": ["Strong team", "Good technology"],
                "weaknesses": ["Limited budget", "Small team"],
                "opportunities": ["Growing market", "Remote work trend"],
                "threats": ["Competition", "Economic uncertainty"],
            },
            "features": {
                "must_have": [
                    {
                        "title": "Task Management",
                        "description": "Core task management functionality",
                        "moscow_rank": "Must-Have",
                    }
                ],
                "should_have": [
                    {
                        "title": "Real-time Chat",
                        "description": "Team communication features",
                        "moscow_rank": "Should-Have",
                    }
                ],
            },
            "roadmap": {
                "project_tasks": [
                    {
                        "task": "MVP Development",
                        "description": "Build minimum viable product",
                        "priority": "high",
                        "duration": 60,
                        "quarter": 1,
                    }
                ]
            },
        }

        # Step 1: Start pipeline
        mock_pipeline_manager.start_new_run.return_value = step_responses[
            "market_research"
        ]

        start_response = await test_client.post(
            "/api/v1/pipeline/start", json=api_test_data["start_request"]
        )

        assert start_response.status_code == status.HTTP_201_CREATED
        start_data = start_response.json()
        run_id = start_data["run_id"]
        assert start_data["step"] == "market_research"
        assert "data" in start_data

        # Step 2: Progress through each pipeline step
        pipeline_steps = ["market_research", "lbc", "persona", "swot", "features"]

        for i, current_step in enumerate(pipeline_steps[:-1]):  # Exclude last step
            next_step = pipeline_steps[i + 1]

            # Mock the next step response
            next_step_response = {"step": next_step, "data": step_responses[next_step]}
            mock_pipeline_manager.trigger_next_step.return_value = next_step_response

            # Call next step endpoint
            response = await test_client.post(
                "/api/v1/pipeline/next",
                json={"run_id": run_id, "current_step": current_step},
            )

            assert response.status_code == status.HTTP_200_OK
            response_data = response.json()
            assert response_data["step"] == next_step
            assert "data" in response_data

        # Step 3: Final step (roadmap)
        mock_pipeline_manager.trigger_next_step.return_value = {
            "step": "roadmap",
            "data": step_responses["roadmap"],
        }

        final_response = await test_client.post(
            "/api/v1/pipeline/next", json={"run_id": run_id, "current_step": "features"}
        )

        assert final_response.status_code == status.HTTP_200_OK
        final_data = final_response.json()
        assert final_data["step"] == "roadmap"
        assert "project_tasks" in final_data["data"]

        # Step 4: Try to go beyond last step (should fail)
        mock_pipeline_manager.trigger_next_step.side_effect = ValueError(
            "Already at the last step of the pipeline."
        )

        beyond_last_response = await test_client.post(
            "/api/v1/pipeline/next", json={"run_id": run_id, "current_step": "roadmap"}
        )

        assert beyond_last_response.status_code == status.HTTP_400_BAD_REQUEST

        # Verify all calls were made correctly
        assert mock_pipeline_manager.start_new_run.call_count == 1
        assert (
            mock_pipeline_manager.trigger_next_step.call_count == 6
        )  # 5 successful + 1 failed

    @patch("src.routes.brainstormer_route.get_pipeline_manager")
    async def test_chat_interaction_workflow(
        self, mock_get_pipeline_manager, test_client: AsyncClient, api_test_data
    ):
        """Test chat interaction workflow during pipeline execution."""
        # Setup mock pipeline manager
        mock_pipeline_manager = AsyncMock()
        mock_get_pipeline_manager.return_value = mock_pipeline_manager

        # Step 1: Start pipeline
        mock_pipeline_manager.start_new_run.return_value = api_test_data[
            "sample_market_research"
        ]

        start_response = await test_client.post(
            "/api/v1/pipeline/start", json=api_test_data["start_request"]
        )

        run_id = start_response.json()["run_id"]

        # Step 2: User asks for clarification
        clarification_response = {
            "response_type": "clarification",
            "message_to_user": "Could you provide more details about your target market?",
            "payload": {},
        }

        mock_response = MagicMock()
        mock_response.dict.return_value = clarification_response
        mock_pipeline_manager.handle_chat_message.return_value = mock_response

        chat_response = await test_client.post(
            "/api/v1/pipeline/chat",
            json={
                "run_id": run_id,
                "current_step": "market_research",
                "message": "Can you explain the market analysis better?",
            },
        )

        assert chat_response.status_code == status.HTTP_200_OK
        chat_data = chat_response.json()
        assert chat_data["response_type"] == "clarification"
        assert "target market" in chat_data["message_to_user"]

        # Step 3: User provides more information and requests edit
        edit_response = {
            "response_type": "edit",
            "message_to_user": "I've updated the market research with your additional information.",
            "payload": {
                "market_summary": "Updated market summary with target market details...",
                "identified_gaps": "Updated gaps analysis...",
                "competitors": [
                    {
                        "name": "Asana",
                        "url": "https://asana.com",
                        "strengths": "Strong features",
                    },
                    {
                        "name": "Trello",
                        "url": "https://trello.com",
                        "strengths": "Simple interface",
                    },
                ],
            },
        }

        mock_response.dict.return_value = edit_response

        edit_chat_response = await test_client.post(
            "/api/v1/pipeline/chat",
            json={
                "run_id": run_id,
                "current_step": "market_research",
                "message": "Please add Trello as a competitor and focus on small to medium businesses as target market.",
            },
        )

        assert edit_chat_response.status_code == status.HTTP_200_OK
        edit_data = edit_chat_response.json()
        assert edit_data["response_type"] == "edit"
        assert "updated" in edit_data["message_to_user"].lower()
        assert len(edit_data["payload"]["competitors"]) == 2

        # Step 4: User requests delegation (search)
        delegate_response = {
            "response_type": "delegate_search",
            "message_to_user": "I'll search for more information about task management competitors.",
            "payload": {"query": "task management software competitors 2024"},
        }

        mock_response.dict.return_value = delegate_response

        delegate_chat_response = await test_client.post(
            "/api/v1/pipeline/chat",
            json={
                "run_id": run_id,
                "current_step": "market_research",
                "message": "Can you find more recent competitors in the task management space?",
            },
        )

        assert delegate_chat_response.status_code == status.HTTP_200_OK
        delegate_data = delegate_chat_response.json()
        assert delegate_data["response_type"] == "delegate_search"
        assert "search" in delegate_data["message_to_user"].lower()
        assert "query" in delegate_data["payload"]

        # Verify all chat interactions were handled
        assert mock_pipeline_manager.handle_chat_message.call_count == 3

    @patch("src.routes.brainstormer_route.get_pipeline_manager")
    async def test_error_recovery_workflow(
        self, mock_get_pipeline_manager, test_client: AsyncClient, api_test_data
    ):
        """Test error recovery in API workflow."""
        # Setup mock pipeline manager
        mock_pipeline_manager = AsyncMock()
        mock_get_pipeline_manager.return_value = mock_pipeline_manager

        # Step 1: Start pipeline successfully
        mock_pipeline_manager.start_new_run.return_value = api_test_data[
            "sample_market_research"
        ]

        start_response = await test_client.post(
            "/api/v1/pipeline/start", json=api_test_data["start_request"]
        )

        run_id = start_response.json()["run_id"]

        # Step 2: Next step fails with internal error
        mock_pipeline_manager.trigger_next_step.side_effect = Exception(
            "AI service temporarily unavailable"
        )

        error_response = await test_client.post(
            "/api/v1/pipeline/next",
            json={"run_id": run_id, "current_step": "market_research"},
        )

        assert error_response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        assert "AI service temporarily unavailable" in error_response.json()["detail"]

        # Step 3: Retry after error (should work)
        mock_pipeline_manager.trigger_next_step.side_effect = None
        mock_pipeline_manager.trigger_next_step.return_value = {
            "step": "lbc",
            "data": {"problem": ["Test problem"], "solution": ["Test solution"]},
        }

        retry_response = await test_client.post(
            "/api/v1/pipeline/next",
            json={"run_id": run_id, "current_step": "market_research"},
        )

        assert retry_response.status_code == status.HTTP_200_OK
        assert retry_response.json()["step"] == "lbc"

        # Step 4: Chat with invalid run_id
        mock_pipeline_manager.handle_chat_message.side_effect = ValueError(
            "Invalid run_id or step_name"
        )

        invalid_chat_response = await test_client.post(
            "/api/v1/pipeline/chat",
            json={
                "run_id": "invalid-run-id",
                "current_step": "market_research",
                "message": "Test message",
            },
        )

        assert invalid_chat_response.status_code == status.HTTP_404_NOT_FOUND
        assert "Invalid run_id" in invalid_chat_response.json()["detail"]

    @patch("src.routes.brainstormer_route.get_pipeline_manager")
    async def test_concurrent_requests_workflow(
        self, mock_get_pipeline_manager, test_client: AsyncClient, api_test_data
    ):
        """Test handling of concurrent requests."""
        # Setup mock pipeline manager
        mock_pipeline_manager = AsyncMock()
        mock_get_pipeline_manager.return_value = mock_pipeline_manager

        # Mock responses
        mock_pipeline_manager.start_new_run.return_value = api_test_data[
            "sample_market_research"
        ]

        # Start multiple pipelines concurrently
        import asyncio

        async def start_pipeline():
            return await test_client.post(
                "/api/v1/pipeline/start", json=api_test_data["start_request"]
            )

        # Start 3 concurrent pipelines
        responses = await asyncio.gather(
            start_pipeline(), start_pipeline(), start_pipeline()
        )

        # All should succeed
        for response in responses:
            assert response.status_code == status.HTTP_201_CREATED
            assert "run_id" in response.json()

        # All run_ids should be unique
        run_ids = [response.json()["run_id"] for response in responses]
        assert len(set(run_ids)) == 3  # All unique

        # Verify all calls were made
        assert mock_pipeline_manager.start_new_run.call_count == 3
