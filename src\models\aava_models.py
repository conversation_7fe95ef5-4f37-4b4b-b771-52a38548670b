from typing import Any, Dict, List, Optional, Union
from fastapi import HTTP<PERSON>xception
from pydantic import BaseModel

from src.config.error_codes import APIErrorCode
from src.models.common import ErrorDetail


class Choice(BaseModel):
    text: str
    index: str
    identifier: str


class Response(BaseModel):
    id: str
    executedPrompt: str
    object: str
    choices: List[Choice]


class DAResponse(BaseModel):
    response: Response


class BaseAPIException(
    HTTPException
):  # Inherit from HTTPException for FastAPI compatibility
    def __init__(
        self,
        status_code: int,
        error_code: APIErrorCode,
        message: Optional[str] = None,
        details: Optional[Union[List[ErrorDetail], str, Dict[str, Any]]] = None,
        headers: Optional[Dict[str, str]] = None,
    ):
        # If no message is provided, use the error_code's value (name) as a default message
        self.message = (
            message
            if message is not None
            else error_code.name.replace("_", " ").title()
        )
        self.error_code = error_code
        self.details = details
        # The content for JSONResponse will be built by the exception handler
        # For HTTPException, 'detail' is what gets sent, but we'll customize it.
        super().__init__(status_code=status_code, detail=self.message, headers=headers)


class DAValueError(BaseAPIException):
    def __init__(
        self,
        message: str = "A business logic validation error occurred.",
        error_code: APIErrorCode = APIErrorCode.BUSINESS_LOGIC_ERROR,
        details: Optional[Union[List[ErrorDetail], str, Dict[str, Any]]] = None,
        status_code: int = 400,  # Default to 400 for DAValueErrors
    ):
        super().__init__(
            status_code=status_code,
            error_code=error_code,
            message=message,
            details=details,
        )


class DAServerError(BaseAPIException):
    def __init__(
        self,
        message: str = "A business logic validation error occurred due to platform instruction being down",
        error_code: APIErrorCode = APIErrorCode.BUSINESS_LOGIC_ERROR,
        details: Optional[Union[List[ErrorDetail], str, Dict[str, Any]]] = None,
        status_code: int = 500,  # Default to 400 for DAValueErrors
    ):
        super().__init__(
            status_code=status_code,
            error_code=error_code,
            message=message,
            details=details,
        )
