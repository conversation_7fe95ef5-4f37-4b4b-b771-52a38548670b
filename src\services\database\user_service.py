import asyncpg
from typing import Dict, Any, List, Optional
from uuid import UUID
from src.utils.db_connection_manager import DatabaseConnectionManager


class UserService:
    def __init__(
        self,
        db_pool: asyncpg.Pool,
        db_manager: Optional[DatabaseConnectionManager] = None,
    ):
        self.db_pool = db_pool
        self.db_manager = db_manager or DatabaseConnectionManager(db_pool)

    async def create_user(
        self,
        email: str,
        full_name: Optional[str] = None,
    ) -> Dict[str, Any]:
        """Create a new user"""
        async with self.db_manager.get_connection() as conn:
            user_id = await conn.fetchval(
                """
                INSERT INTO brainstormer.users (email, full_name)
                VALUES ($1, $2)
                RETURNING user_id
            """,
                email,
                full_name,
            )

            return {
                "user_id": str(user_id),
                "email": email,
                "full_name": full_name,
            }

    async def get_user_by_id(self, user_id: UUID) -> Optional[Dict[str, Any]]:
        """Get user by user_id"""
        async with self.db_manager.get_connection() as conn:
            user_data = await conn.fetchrow(
                """
                SELECT user_id, email, full_name, created_at
                FROM brainstormer.users
                WHERE user_id = $1
            """,
                user_id,
            )

            if not user_data:
                return None

            return {
                "user_id": str(user_data["user_id"]),
                "email": user_data["email"],
                "full_name": user_data["full_name"],
                "created_at": user_data["created_at"],
            }

    async def get_user_by_email(self, email: str) -> Optional[Dict[str, Any]]:
        """Get user by email"""
        async with self.db_manager.get_connection() as conn:
            user_data = await conn.fetchrow(
                """
                SELECT user_id, email, full_name, created_at
                FROM brainstormer.users
                WHERE email = $1
            """,
                email,
            )

            if not user_data:
                return None

            return {
                "user_id": str(user_data["user_id"]),
                "email": user_data["email"],
                "full_name": user_data["full_name"],
                "created_at": user_data["created_at"],
            }

    async def update_user(
        self,
        user_id: UUID,
        email: Optional[str] = None,
        full_name: Optional[str] = None,
    ) -> Optional[Dict[str, Any]]:
        """Update user information"""
        async with self.db_manager.get_connection() as conn:
            # Build dynamic update query
            update_fields = []
            params = [user_id]
            param_count = 1

            if email is not None:
                param_count += 1
                update_fields.append(f"email = ${param_count}")
                params.append(email)

            if full_name is not None:
                param_count += 1
                update_fields.append(f"full_name = ${param_count}")
                params.append(full_name)

            if not update_fields:
                # No fields to update, return current user
                return await self.get_user_by_id(user_id)

            query = f"""
                UPDATE brainstormer.users
                SET {', '.join(update_fields)}
                WHERE user_id = $1
                RETURNING user_id, email, full_name, created_at
            """

            user_data = await conn.fetchrow(query, *params)

            if not user_data:
                return None

            return {
                "user_id": str(user_data["user_id"]),
                "email": user_data["email"],
                "full_name": user_data["full_name"],
                "created_at": user_data["created_at"],
            }

    async def delete_user(self, user_id: UUID) -> bool:
        """Delete a user"""
        async with self.db_manager.get_connection() as conn:
            result = await conn.execute(
                """
                DELETE FROM brainstormer.users WHERE user_id = $1
            """,
                user_id,
            )
            # Return True if a row was deleted
            return result == "DELETE 1"

    async def list_users(
        self, limit: int = 100, offset: int = 0
    ) -> List[Dict[str, Any]]:
        """List all users with pagination"""
        async with self.db_manager.get_connection() as conn:
            users = await conn.fetch(
                """
                SELECT user_id, email, full_name, created_at
                FROM brainstormer.users
                ORDER BY created_at DESC
                LIMIT $1 OFFSET $2
            """,
                limit,
                offset,
            )

            return [
                {
                    "user_id": str(user["user_id"]),
                    "email": user["email"],
                    "full_name": user["full_name"],
                    "created_at": user["created_at"],
                }
                for user in users
            ]
