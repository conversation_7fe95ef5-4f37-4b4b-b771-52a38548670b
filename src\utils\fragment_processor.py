import json
from src.models.Agents.Agent import PR_CompetitorAnalysisFragment, PR_SwotFragment,PR_FeatureFragment,PR_UnderstandingFragment_V2, Schema_PersonaFragment,PR_UnderstandingFragment,  PR_RoadmapFragment
from src.models.models import BrainstormerRequest
import json
import os
class FragmentProcessor:
    # A  memeber function that converts Fragments to dict, json, xml and any custom output parser format,
    def __init__(self, request):
        self.recipe:str = ""
        self.request:BrainstormerRequest = request
        self.user_application_domain = None
        self.user_app_name = None
        self.user_app_summary = None
        self.user_app_all_features = None

    def get_input_prompt(self, usecase):
        input_data = None
        schema = None

        if "MLO_PR_SWOT_ANALYSIS" in usecase:
            input_data = self.request.model_dump_json(include={"understanding", "feature"})
            schema = PR_SwotFragment.model_json_schema()

        elif "MLO_PR_FEATURE_DISCUSSION" in usecase:
            def clean_persona(input_data_dict):
                def remove_keys(data, keys):
                    for key in keys:
                        data.pop(key, None)

                input_data_dict.pop("base", None)

                understanding = input_data_dict.get("understanding", {})
                fields_to_remove = [
                    "alternatives", "keyMetrics", "highLevelConcepts", 
                    "costStructure", "earlyAdopters", "revenueStreams", 
                    "customerSegments"
                ]
                remove_keys(understanding, fields_to_remove)

                for persona in input_data_dict.get("persona", []):
                    remove_keys(persona, ["smartphone_usage", "tablet_usage", "laptop_usage", "role"])

                    demographics = persona.get("demographics", {})
                    remove_keys(demographics, ["gender", "profession", "education"])

                return input_data_dict

            input_data = self.request.model_dump_json(include={"base", "understanding", "persona"})
            input_data_dict = json.loads(input_data) if input_data else {}
            input_data_dict = clean_persona(input_data_dict)
            input_data = json.dumps(input_data_dict)
            schema = ""

        elif "MLO_PR_PERSONA" in usecase:
            appSummary = self.request.understanding.summary
            userGroups = self.request.base.user_type
            input_data = "App description: " + appSummary + "\n userGroups for the app: " + ",".join(userGroups)
            schema = Schema_PersonaFragment.model_json_schema()

        elif "TEST_UNDERSTANDING" in usecase:
            input_data = self.request.model_dump_json(include={"base"})
            return "Your input data:\n" + input_data

        elif "MLO_PR_UNDERSTANDING" in usecase:
            input_data = self.request.model_dump_json(include={"base"})
            schema = PR_UnderstandingFragment_V2.model_json_schema()

        elif "MLO_PR_COMPETITOR_ANALYSIS" in usecase:
            input_data = self.request.model_dump_json(include={"understanding", "feature"})
            input_data_json = json.loads(input_data)

            self.user_app_name = input_data_json["understanding"].get("appName")
            self.user_app_summary = input_data_json["understanding"].get("summary")
            self.user_app_all_features = [
                feature.get("title") for feature in input_data_json["feature"].get("must_have", []) +
                input_data_json["feature"].get("should_have", [])
            ]

            schema = PR_CompetitorAnalysisFragment.model_json_schema()

        elif "MLO_PR_ROADMAP" in usecase:
            input_data = self.request.model_dump_json(include={"understanding", "feature"})
            schema = PR_RoadmapFragment.model_json_schema()

        else:
            return "Unknown"

        return self.prepare_input(input_data, schema)
    

    def generate_question_string(self):
        """Generates the question string to ask based on the app name, summary, and features."""

        question_string = f"""
        So, my application name is {self.user_app_name}. It's a {self.user_app_summary}.
        The application has the following features: {', '.join(self.user_app_all_features)}.
        Based on these details, who would be the best competitors in the market?
        """

        return question_string
            
    def prepare_input(self,input,schema):
        final_prompt = """As an example, for the schema {{"properties": {{"foo": {{"title": "Foo", "description": "a list of strings", "type": "array", "items": {{"type": "string"}}}}}}, "required": ["foo"]}} the object {{"foo": ["bar", "baz"]}} is a well-formatted instance of the schema. The object {{"properties": {{"foo": ["bar", "baz"]}}}} is not well-formatted. \n Here is the output schema:```\n{schema}\n```\n Your JSON output should be well formatted.\nYour input\n{input}\n IMPORTANT:
Just return the JSON without any ```json markdown. Do not add any other instructions""".format_map({"schema": schema, "input": input})

        return final_prompt
    
    def get_output_fragment(self, usecase, input_data):
        if "MLO_PR_SWOT_ANALYSIS" in usecase:
            return PR_SwotFragment.model_validate_json(input_data)
        elif "MLO_PR_FEATURE_DISCUSSION" in usecase:
            feature = PR_FeatureFragment.model_validate_json(input_data)
            return feature
        elif "MLO_PR_UNDERSTANDING" in usecase:
            print("prevalidatin:", input_data)
            understanding = PR_UnderstandingFragment_V2.model_validate_json(input_data)
            print("understanding validation", understanding)
            return understanding
        elif "MLO_PR_PERSONA" in usecase:
            persona = Schema_PersonaFragment.model_validate_json(input_data)
            print(persona.personas)
            return persona.personas
        elif "TEST_UNDERSTANDING" in usecase:
            invalid_json = json.loads(input_data)
            if "properties" in invalid_json:
                input_data = json.dumps(invalid_json["properties"])
            understanding = PR_UnderstandingFragment.model_validate_json(input_data)
            return understanding
        elif "MLO_PR_COMPETITOR_ANALYSIS" in usecase:
            analysis = PR_CompetitorAnalysisFragment.model_validate_json(input_data)
            return analysis
        elif "MLO_PR_ROADMAP" in usecase:
            roadmap = PR_RoadmapFragment.model_validate_json(input_data)
            return roadmap
        else:
            return None