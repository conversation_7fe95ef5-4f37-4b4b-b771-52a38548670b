"""
Test configuration and fixtures for the test suite.
"""

import asyncio
import asyncpg
import pytest
from typing import Async<PERSON>enerator
from uuid import uuid4
import os
from unittest.mock import AsyncMock, MagicMock
from fastapi.testclient import TestClient


# Configure pytest-asyncio
pytest_plugins = ("pytest_asyncio",)


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="session")
async def test_db_pool() -> AsyncGenerator[asyncpg.Pool, None]:
    """Create a test database pool using existing database."""
    # Use environment variables or defaults for test database
    db_host = os.getenv("TEST_DB_HOST", "localhost")
    db_port = int(os.getenv("TEST_DB_PORT", "5432"))
    db_user = os.getenv("TEST_DB_USER", "postgres")
    db_password = os.getenv("TEST_DB_PASSWORD", "")
    db_name = os.getenv("TEST_DB_NAME", "product_studio_test")

    try:
        # Create pool for test database
        pool = await asyncpg.create_pool(
            host=db_host,
            port=db_port,
            user=db_user,
            password=db_password,
            database=db_name,
            min_size=1,
            max_size=5,
        )

        # Set up database schema
        async with pool.acquire() as conn:
            # Read and execute schema
            schema_path = os.path.join(
                os.path.dirname(__file__), "..", "src", "database", "schema.sql"
            )
            with open(schema_path, "r") as f:
                schema_sql = f.read()
            await conn.execute(schema_sql)

        yield pool

        # Cleanup
        await pool.close()

    except Exception as e:
        pytest.skip(f"Database not available for testing: {e}")


@pytest.fixture
async def clean_db(test_db_pool: asyncpg.Pool):
    """Clean database before each test."""
    async with test_db_pool.acquire() as conn:
        # Clean all tables in reverse order to respect foreign keys
        await conn.execute("DELETE FROM brainstormer.project_conversations")
        await conn.execute("DELETE FROM brainstormer.roadmap_tasks")
        await conn.execute("DELETE FROM brainstormer.features")
        await conn.execute("DELETE FROM brainstormer.swot_analysis")
        await conn.execute("DELETE FROM brainstormer.user_personas")
        await conn.execute("DELETE FROM brainstormer.lean_business_canvas")
        await conn.execute("DELETE FROM brainstormer.competitors")
        await conn.execute("DELETE FROM brainstormer.market_research")
        await conn.execute("DELETE FROM brainstormer.projects")
        await conn.execute("DELETE FROM brainstormer.users")

    yield

    # Clean up after test as well
    async with test_db_pool.acquire() as conn:
        await conn.execute("DELETE FROM brainstormer.project_conversations")
        await conn.execute("DELETE FROM brainstormer.roadmap_tasks")
        await conn.execute("DELETE FROM brainstormer.features")
        await conn.execute("DELETE FROM brainstormer.swot_analysis")
        await conn.execute("DELETE FROM brainstormer.user_personas")
        await conn.execute("DELETE FROM brainstormer.lean_business_canvas")
        await conn.execute("DELETE FROM brainstormer.competitors")
        await conn.execute("DELETE FROM brainstormer.market_research")
        await conn.execute("DELETE FROM brainstormer.projects")
        await conn.execute("DELETE FROM brainstormer.users")


@pytest.fixture
async def test_user_id(test_db_pool: asyncpg.Pool, clean_db) -> str:
    """Create a test user and return the user_id."""
    async with test_db_pool.acquire() as conn:
        user_id = await conn.fetchval(
            """
            INSERT INTO brainstormer.users (email, full_name)
            VALUES ($1, $2)
            RETURNING user_id
            """,
            "<EMAIL>",
            "Test User",
        )
    return user_id


@pytest.fixture
def sample_project_data():
    """Sample project data for testing."""
    return {
        "name": "Test Project",
        "description": "A test project for unit testing",
        "run_id": uuid4(),
    }


@pytest.fixture
def sample_market_research_data():
    """Sample market research data for testing."""
    return {
        "market_summary": "This is a test market summary with detailed analysis.",
        "identified_gaps": "Gap 1: Missing feature X. Gap 2: Poor user experience.",
        "competitors": [
            {
                "name": "Competitor A",
                "url": "https://competitor-a.com",
                "strengths": "Strong brand recognition and user base",
            },
            {
                "name": "Competitor B",
                "url": "https://competitor-b.com",
                "strengths": "Advanced technology and features",
            },
        ],
    }


@pytest.fixture
def sample_lbc_data():
    """Sample lean business canvas data for testing."""
    return {
        "problem": ["Problem 1", "Problem 2"],
        "solution": ["Solution 1", "Solution 2"],
        "key_partners": ["Partner 1", "Partner 2"],
        "value_proposition": ["Value 1", "Value 2"],
        "customer_segments": ["Segment 1", "Segment 2"],
        "revenue_streams": ["Revenue 1", "Revenue 2"],
        "key_metrics": ["Metric 1", "Metric 2"],
        "alternatives": ["Alternative 1", "Alternative 2"],
        "solution_tenants": ["Tenant 1", "Tenant 2"],
    }


@pytest.fixture
def sample_personas_data():
    """Sample user personas data for testing."""
    return [
        {
            "name": "John Doe",
            "role": "Software Developer",
            "age": 30,
            "gender": "male",
            "education": "Bachelor's in Computer Science",
            "status": "Employed",
            "location": "San Francisco, CA",
            "tech_literacy": "High",
            "avatar": "https://example.com/avatar1.jpg",
            "quote": "I love building great software",
            "personality": ["Analytical", "Detail-oriented"],
            "pain_points": ["Time constraints", "Complex requirements"],
            "goals": ["Build quality software", "Learn new technologies"],
            "motivation": ["Career growth", "Technical challenges"],
            "expectations": ["Good tools", "Clear requirements"],
            "devices": ["MacBook Pro", "iPhone"],
        },
        {
            "name": "Jane Smith",
            "role": "Product Manager",
            "age": 28,
            "gender": "female",
            "education": "MBA",
            "status": "Employed",
            "location": "New York, NY",
            "tech_literacy": "Medium",
            "avatar": "https://example.com/avatar2.jpg",
            "quote": "User experience is everything",
            "personality": ["Strategic", "User-focused"],
            "pain_points": ["Stakeholder alignment", "Resource constraints"],
            "goals": ["Deliver great products", "Understand users"],
            "motivation": ["User satisfaction", "Business impact"],
            "expectations": ["Data-driven decisions", "User feedback"],
            "devices": ["MacBook Air", "Android phone"],
        },
    ]


@pytest.fixture
def sample_features_data():
    """Sample features data for testing."""
    return [
        {
            "category": "must_have",
            "title": "User Authentication",
            "description": "Secure user login and registration",
            "tenant": "Security",
            "justification": "Essential for user data protection",
            "tags": ["security", "authentication"],
            "moscow_rank": "Must-Have",
        },
        {
            "category": "should_have",
            "title": "Dashboard Analytics",
            "description": "Real-time analytics dashboard",
            "tenant": "Analytics",
            "justification": "Important for user insights",
            "tags": ["analytics", "dashboard"],
            "moscow_rank": "Should-Have",
        },
    ]


@pytest.fixture
def sample_swot_data():
    """Sample SWOT analysis data for testing."""
    return {
        "strengths": ["Strong team", "Good technology", "Market knowledge"],
        "weaknesses": ["Limited budget", "Small team", "New to market"],
        "opportunities": ["Growing market", "New technologies", "Partnerships"],
        "threats": ["Competition", "Economic downturn", "Regulatory changes"],
    }


@pytest.fixture
def sample_roadmap_tasks_data():
    """Sample roadmap tasks data for testing."""
    return [
        {
            "task": "Setup Development Environment",
            "description": "Configure development tools and environment",
            "priority": "high",
            "duration": 5,
            "quarter": 1,
        },
        {
            "task": "Implement Core Features",
            "description": "Build the main application features",
            "priority": "high",
            "duration": 30,
            "quarter": 1,
        },
        {
            "task": "User Testing",
            "description": "Conduct user acceptance testing",
            "priority": "medium",
            "duration": 10,
            "quarter": 2,
        },
    ]


# API Testing Fixtures
@pytest.fixture
def mock_db_pool():
    """Create a mock database pool for API testing."""
    mock_pool = MagicMock()
    mock_conn = AsyncMock()
    mock_pool.acquire.return_value.__aenter__.return_value = mock_conn
    return mock_pool


@pytest.fixture
def mock_redis_manager():
    """Create a mock Redis manager for API testing."""
    mock_redis = AsyncMock()
    mock_redis.save_step_data = AsyncMock()
    mock_redis.load_step_data = AsyncMock()
    return mock_redis


@pytest.fixture
def mock_pipeline_agent():
    """Create a mock pipeline agent for API testing."""
    mock_agent = AsyncMock()
    mock_agent.generate_artifact = AsyncMock()
    return mock_agent


@pytest.fixture
def mock_conversational_agent():
    """Create a mock conversational agent for API testing."""
    mock_agent = AsyncMock()
    mock_agent.get_response = AsyncMock()
    mock_agent.set_project_service = MagicMock()
    return mock_agent


@pytest.fixture
def mock_project_service():
    """Create a mock project service for API testing."""
    mock_service = AsyncMock()

    # Mock all project service methods
    mock_service.create_project = AsyncMock()
    mock_service.get_project_by_run_id = AsyncMock()
    mock_service.update_market_research = AsyncMock()
    mock_service.get_market_research = AsyncMock()
    mock_service.update_lean_business_canvas = AsyncMock()
    mock_service.get_lean_business_canvas = AsyncMock()
    mock_service.update_user_personas = AsyncMock()
    mock_service.get_user_personas = AsyncMock()
    mock_service.update_swot_analysis = AsyncMock()
    mock_service.get_swot_analysis = AsyncMock()
    mock_service.update_features = AsyncMock()
    mock_service.get_features = AsyncMock()
    mock_service.update_roadmap_tasks = AsyncMock()
    mock_service.get_roadmap_tasks = AsyncMock()
    mock_service.add_conversation_message = AsyncMock()
    mock_service.get_conversation_messages = AsyncMock()

    return mock_service


@pytest.fixture
def test_client():
    """Create a test client for API testing."""
    from src.main import app

    # Mock the database pool in app state
    mock_pool = MagicMock()
    app.state.db_pool = mock_pool

    # Use TestClient for synchronous testing
    client = TestClient(app)
    return client


@pytest.fixture
async def async_test_client():
    """Create an async test client for API testing."""
    from httpx import AsyncClient
    from src.main import app

    # Mock the database pool in app state
    mock_pool = MagicMock()
    app.state.db_pool = mock_pool

    # Mock authentication to return a test user
    from unittest.mock import patch

    with patch("src.security.auth_helper.get_authenticated_user") as mock_auth:
        mock_auth.return_value = {
            "email": "<EMAIL>",
            "user_id": "test-user-123",
        }

        async with AsyncClient(app=app, base_url="http://test") as client:
            yield client


@pytest.fixture
def api_test_data():
    """Sample data for API testing."""
    return {
        "start_request": {
            "user_idea": "I want to build a task management app for remote teams"
        },
        "chat_request": {
            "run_id": "test-run-id-123",
            "current_step": "market_research",
            "message": "Can you add more competitors to the analysis?",
        },
        "next_step_request": {
            "run_id": "test-run-id-123",
            "current_step": "market_research",
        },
        "sample_market_research": {
            "market_summary": "The task management market is growing rapidly...",
            "identified_gaps": "Lack of real-time collaboration features...",
            "competitors": [
                {
                    "name": "Asana",
                    "url": "https://asana.com",
                    "strengths": "Strong project management features",
                }
            ],
        },
        "sample_conversational_response": {
            "response_type": "edit",
            "message_to_user": "I've updated the market research with additional competitors.",
            "payload": {
                "market_summary": "Updated market summary...",
                "competitors": [
                    {
                        "name": "Asana",
                        "url": "https://asana.com",
                        "strengths": "Strong project management",
                    },
                    {
                        "name": "Trello",
                        "url": "https://trello.com",
                        "strengths": "Simple kanban boards",
                    },
                ],
            },
        },
    }
