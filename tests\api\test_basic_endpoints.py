"""
Basic API endpoint tests without complex mocking.
"""

from fastapi.testclient import <PERSON><PERSON><PERSON>
from fastapi import status
from unittest.mock import MagicMock, patch


class TestBasicAPIEndpoints:
    """Test class for basic API endpoints."""

    def test_root_endpoint(self, test_client: TestClient):
        """Test the root endpoint."""
        response = test_client.get("/")
        assert response.status_code == status.HTTP_200_OK
        assert response.json() == "Welcome to the FastAPI application!"

    def test_heartbeat_endpoint(self, test_client: TestClient):
        """Test the heartbeat endpoint."""
        response = test_client.get("/heartbeat")
        assert response.status_code == status.HTTP_200_OK
        assert response.json() == {"status": "ok"}

    def test_invalid_endpoint(self, test_client: TestClient):
        """Test request to non-existent endpoint."""
        response = test_client.get("/api/v1/nonexistent")
        assert response.status_code == status.HTTP_404_NOT_FOUND

    def test_invalid_method(self, test_client: TestClient):
        """Test invalid HTTP method on existing endpoint."""
        response = test_client.get("/api/v1/pipeline/start")
        assert response.status_code == status.HTTP_405_METHOD_NOT_ALLOWED

    def test_start_pipeline_missing_user_idea(self, test_client: TestClient):
        """Test pipeline start with missing user_idea."""
        response = test_client.post("/api/v1/pipeline/start", json={})
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        response_data = response.json()
        assert "detail" in response_data

    def test_start_pipeline_empty_user_idea(self, test_client: TestClient):
        """Test pipeline start with empty user_idea."""
        response = test_client.post("/api/v1/pipeline/start", json={"user_idea": ""})
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    def test_start_pipeline_null_user_idea(self, test_client: TestClient):
        """Test pipeline start with null user_idea."""
        response = test_client.post("/api/v1/pipeline/start", json={"user_idea": None})
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    def test_next_step_missing_fields(self, test_client: TestClient):
        """Test next step with missing required fields."""
        response = test_client.post("/api/v1/pipeline/next", json={})
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

        response = test_client.post(
            "/api/v1/pipeline/next", json={"run_id": "test-run-id"}
        )
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

        response = test_client.post(
            "/api/v1/pipeline/next", json={"current_step": "market_research"}
        )
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    def test_chat_missing_fields(self, test_client: TestClient):
        """Test chat with missing required fields."""
        response = test_client.post("/api/v1/pipeline/chat", json={})
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

        response = test_client.post(
            "/api/v1/pipeline/chat", json={"run_id": "test-run-id"}
        )
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

        response = test_client.post(
            "/api/v1/pipeline/chat",
            json={"run_id": "test-run-id", "current_step": "market_research"},
        )
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    def test_malformed_json(self, test_client: TestClient):
        """Test handling of malformed JSON."""
        response = test_client.post(
            "/api/v1/pipeline/start",
            content='{"user_idea": "test"',  # Missing closing brace
            headers={"Content-Type": "application/json"},
        )
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    def test_unicode_content(self, test_client: TestClient):
        """Test handling of Unicode content."""
        # This should be accepted as valid input
        response = test_client.post(
            "/api/v1/pipeline/start",
            json={"user_idea": "I want to build an app with émojis 🚀"},
        )
        # Should either succeed or fail gracefully (not crash)
        assert response.status_code in [
            status.HTTP_201_CREATED,
            status.HTTP_500_INTERNAL_SERVER_ERROR,
        ]

    @patch("src.routes.brainstormer_route.get_pipeline_manager")
    def test_start_pipeline_with_mock_success(
        self, mock_get_pipeline_manager, test_client: TestClient
    ):
        """Test successful pipeline start with mocked dependencies."""
        # Setup mock
        mock_pipeline_manager = MagicMock()
        mock_pipeline_manager.start_new_run.return_value = {
            "market_summary": "Test market summary",
            "identified_gaps": "Test gaps",
            "competitors": [],
        }
        mock_get_pipeline_manager.return_value = mock_pipeline_manager

        # Make request
        response = test_client.post(
            "/api/v1/pipeline/start", json={"user_idea": "Build a task management app"}
        )

        # Should succeed or fail gracefully
        assert response.status_code in [
            status.HTTP_201_CREATED,
            status.HTTP_500_INTERNAL_SERVER_ERROR,
        ]

        if response.status_code == status.HTTP_201_CREATED:
            response_data = response.json()
            assert "run_id" in response_data
            assert response_data["step"] == "market_research"
            assert "data" in response_data

    @patch("src.routes.brainstormer_route.get_pipeline_manager")
    def test_start_pipeline_with_mock_error(
        self, mock_get_pipeline_manager, test_client: TestClient
    ):
        """Test pipeline start with mocked error."""
        # Setup mock to raise exception
        mock_pipeline_manager = MagicMock()
        mock_pipeline_manager.start_new_run.side_effect = Exception("Test error")
        mock_get_pipeline_manager.return_value = mock_pipeline_manager

        response = test_client.post(
            "/api/v1/pipeline/start", json={"user_idea": "Build a task management app"}
        )

        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        response_data = response.json()
        assert "detail" in response_data

    @patch("src.routes.brainstormer_route.get_pipeline_manager")
    def test_next_step_with_mock_success(
        self, mock_get_pipeline_manager, test_client: TestClient
    ):
        """Test successful next step with mocked dependencies."""
        # Setup mock
        mock_pipeline_manager = MagicMock()
        mock_pipeline_manager.trigger_next_step.return_value = {
            "step": "lbc",
            "data": {"problem": ["Test problem"], "solution": ["Test solution"]},
        }
        mock_get_pipeline_manager.return_value = mock_pipeline_manager

        response = test_client.post(
            "/api/v1/pipeline/next",
            json={"run_id": "test-run-id", "current_step": "market_research"},
        )

        # Should succeed or fail gracefully
        assert response.status_code in [
            status.HTTP_200_OK,
            status.HTTP_400_BAD_REQUEST,
            status.HTTP_500_INTERNAL_SERVER_ERROR,
        ]

    @patch("src.routes.brainstormer_route.get_pipeline_manager")
    def test_chat_with_mock_success(
        self, mock_get_pipeline_manager, test_client: TestClient
    ):
        """Test successful chat with mocked dependencies."""
        # Setup mock
        mock_pipeline_manager = MagicMock()
        mock_response = MagicMock()
        mock_response.dict.return_value = {
            "response_type": "clarification",
            "message_to_user": "Test response",
            "payload": {},
        }
        mock_pipeline_manager.handle_chat_message.return_value = mock_response
        mock_get_pipeline_manager.return_value = mock_pipeline_manager

        response = test_client.post(
            "/api/v1/pipeline/chat",
            json={
                "run_id": "test-run-id",
                "current_step": "market_research",
                "message": "Test message",
            },
        )

        # Should succeed or fail gracefully
        assert response.status_code in [
            status.HTTP_200_OK,
            status.HTTP_404_NOT_FOUND,
            status.HTTP_500_INTERNAL_SERVER_ERROR,
        ]

    def test_cors_headers_present(self, test_client: TestClient):
        """Test that CORS is configured (basic check)."""
        response = test_client.get("/heartbeat")
        assert response.status_code == status.HTTP_200_OK
        # In test environment, CORS headers might not be exactly the same
        # but the endpoint should still work

    def test_request_content_type_validation(self, test_client: TestClient):
        """Test content type validation."""
        # Test with wrong content type
        response = test_client.post(
            "/api/v1/pipeline/start",
            content="user_idea=test",
            headers={"Content-Type": "application/x-www-form-urlencoded"},
        )
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
