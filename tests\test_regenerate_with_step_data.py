#!/usr/bin/env python3
"""
Test script to demonstrate the updated regenerate_data_portion method
that can now load step data from Redis/database.
"""

import asyncio
import json
from unittest.mock import AsyncMock
from src.services.aava_service import AAVAAIService


async def test_regenerate_with_step_data():
    """Test the regenerate_data_portion method with step data loading."""

    print("🧪 Testing regenerate_data_portion with step data loading...")

    # Create a mock AAVAAIService instance
    service = AAVAAIService("http://test.com")

    # Mock the project service
    mock_project_service = AsyncMock()
    mock_project_service.get_project_by_run_id.return_value = {"id": 123}
    mock_project_service.get_market_research.return_value = {
        "market_summary": "Test market with growing demand for eco-friendly solutions",
        "competitors": [
            {
                "name": "GreenTech Solutions",
                "url": "http://greentech.com",
                "strengths": "Strong brand recognition",
            },
            {
                "name": "EcoServices Inc",
                "url": "http://ecoservices.com",
                "strengths": "Wide service coverage",
            },
        ],
        "identified_gaps": "Lack of affordable eco-friendly options for small businesses",
    }

    # Inject the project service into the AAVAAIService instance
    service.project_service = mock_project_service

    # Mock the Redis manager
    from unittest.mock import patch

    with patch("src.services.aava_service.RedisManager") as mock_redis_class:
        mock_redis = AsyncMock()
        mock_redis.load_step_data.return_value = (
            None  # Simulate Redis miss, force DB fallback
        )
        mock_redis_class.return_value = mock_redis

        # Mock the API call to return a sample response
        service.make_api_call = AsyncMock(
            return_value={
                "response": {
                    "choices": [
                        {
                            "message": {
                                "content": """```json
{
    "problem": [
        "Small businesses struggle to find affordable eco-friendly service providers",
        "Limited transparency in environmental impact of service providers"
    ],
    "solution": [
        "Platform connecting small businesses with verified eco-friendly service providers",
        "Environmental impact scoring system for all services"
    ]
}
```"""
                            }
                        }
                    ]
                }
            }
        )

        # Test data
        current_data = {
            "problem": [
                "Users struggle to find reliable local services",
                "Lack of transparency in service provider ratings",
            ],
            "solution": ["Platform connecting users with verified providers"],
        }

        user_request = (
            "Make this more focused on eco-friendly services for small businesses"
        )

        # Step information with run_id and step_name
        step_info = {"run_id": "test-run-123", "step_name": "market_research"}

        print("📋 Test Parameters:")
        print(f"   Run ID: {step_info['run_id']}")
        print(f"   Step: {step_info['step_name']}")
        print(f"   User Request: {user_request}")
        print(f"   Current Data: {json.dumps(current_data, indent=2)}")

        try:
            # Call the regenerate method
            result = await service.regenerate_data_portion(
                current_data=current_data,
                user_request=user_request,
                user_signature="<EMAIL>",
                step=step_info,
            )

            print("\n✅ Success! Regeneration completed.")
            print("📤 Result:")
            print(json.dumps(result, indent=2))

            # Verify that the project service was called to load step data
            mock_project_service.get_project_by_run_id.assert_called_once_with(
                "test-run-123"
            )
            mock_project_service.get_market_research.assert_called_once_with(123)

            print("\n🔍 Verification:")
            print("   ✓ Project service was called to load step data")
            print("   ✓ Market research data was retrieved from database")
            print("   ✓ Step data was included in the regeneration context")

        except Exception as e:
            print(f"\n❌ Error during regeneration: {e}")
            raise


async def test_regenerate_without_step_data():
    """Test the regenerate_data_portion method without step data (legacy behavior)."""

    print("\n🧪 Testing regenerate_data_portion without step data (legacy mode)...")

    # Create a mock AAVAAIService instance
    service = AAVAAIService("http://test.com")

    # Mock the API call to return a sample response
    service.make_api_call = AsyncMock(
        return_value={
            "response": {
                "choices": [
                    {
                        "message": {
                            "content": """```json
{
    "problem": [
        "Updated problem statement based on user request"
    ],
    "solution": [
        "Updated solution based on user request"
    ]
}
```"""
                        }
                    }
                ]
            }
        }
    )

    # Test data
    current_data = {"problem": ["Original problem"], "solution": ["Original solution"]}

    user_request = "Update this to be more specific"

    print("📋 Test Parameters:")
    print(f"   User Request: {user_request}")
    print(f"   Current Data: {json.dumps(current_data, indent=2)}")
    print("   Step Info: None (legacy mode)")

    try:
        # Call the regenerate method without step info
        result = await service.regenerate_data_portion(
            current_data=current_data,
            user_request=user_request,
            user_signature="<EMAIL>",
            step=None,  # No step data
        )

        print("\n✅ Success! Legacy regeneration completed.")
        print("📤 Result:")
        print(json.dumps(result, indent=2))

        print("\n🔍 Verification:")
        print("   ✓ Method works without step data (backward compatibility)")
        print("   ✓ Empty step_data was used in prompt formatting")

    except Exception as e:
        print(f"\n❌ Error during legacy regeneration: {e}")
        raise


async def main():
    """Run all tests."""
    print("🚀 Starting regenerate_data_portion tests...\n")

    await test_regenerate_with_step_data()
    await test_regenerate_without_step_data()

    print("\n🎉 All tests completed successfully!")
    print("\n📝 Summary:")
    print("   • The regenerate_data_portion method now loads step data from Redis/DB")
    print("   • Step data is included as context in the regeneration prompt")
    print("   • Backward compatibility is maintained for calls without step info")
    print("   • The method gracefully handles missing data and continues processing")


if __name__ == "__main__":
    asyncio.run(main())
