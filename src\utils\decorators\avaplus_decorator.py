# src/utils/decorators.py
from fastapi import HTTPException
from pydantic import ValidationError


def handle_ava_exceptions(logger):
    def decorator(func):
        async def wrapper(*args, **kwargs):
            try:
                return await func(*args, **kwargs)
            except ValidationError as e:
                logger.error(f"Validation error: {e}")
                raise
            except Exception as e:
                logger.error(f"Unexpected error: {e}")
                raise HTTPException(status_code=500, detail=f"Unexpected error: {e}")

        return wrapper

    return decorator
