# Docker Environment Configuration
# Copy this file to .env for local Docker development

# Database Configuration (for local development - use Docker environment variables in production)
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=postgres
DB_NAME=product_studio

# Redis Configuration (for local development)
REDIS_URL=redis://localhost:6379/0

# API Configuration
BASE_URL=https://avaplus-internal.avateam.io
PIPELINE_AGENT_SERVICE_ENDPOINT=v1/api/instructions/ava/force/workflow-executions
INDIVIDUAL_AGENT_SERVICE_ENDPOINT=v1/api/instructions/ava/force/individualAgent/execute

# Access Key (use environment variable in production)
ACCESS_KEY=eyJraWQiOiIyYjI0OTgzMC00MjE1LTQ5ZWYtYTU4OS00MzgxM2I0MTdhNzQiLCJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************.ssEfELNz_F-XymZN8zVpYhwFvpdESrL76Ln9iFxkz6xzWHJUiSlASRVdI_R_hZsnmxwfnEdK3FnsrLmWKh86ag

# Application Configuration
DEBUG=false
APP_NAME=Product Studio Server

# Kubernetes/Proxy Configuration
# Set this to your Kubernetes ingress path prefix (e.g., /server/product)
ROOT_PATH=/server/product
