#!/usr/bin/env python3
"""
Demonstrate the actual API calls that would be made to run through the complete pipeline.
This script shows the exact sequence of HTTP requests that would be made.
"""

import json
import time
import requests
from typing import Dict, Any, Optional

# Pipeline steps from the configuration
PIPELINE_STEPS = ["market_research", "lbc", "persona", "swot", "features", "roadmap"]


class PipelineRunner:
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.api_base = f"{base_url}/api/v1"

    def start_pipeline(self, user_idea: str) -> Optional[Dict[str, Any]]:
        """Start the pipeline with /pipeline/start endpoint."""
        print("🚀 Starting Pipeline")
        print(f"💡 User Idea: {user_idea}")
        print("-" * 60)

        url = f"{self.api_base}/pipeline/start"
        payload = {"user_idea": user_idea}

        print(f"📡 POST {url}")
        print(f"📝 Payload: {json.dumps(payload, indent=2)}")

        try:
            response = requests.post(url, json=payload, timeout=30)
            print(f"📊 Status Code: {response.status_code}")

            if response.status_code == 201:
                data = response.json()
                print("✅ SUCCESS: Pipeline started")
                print(f"🆔 Run ID: {data.get('run_id')}")
                print(f"📋 Current Step: {data.get('step')}")
                print(f"📄 Data Keys: {list(data.get('data', {}).keys())}")
                return data
            else:
                print(f"❌ FAILED: {response.status_code}")
                try:
                    error_data = response.json()
                    print(f"📋 Error: {json.dumps(error_data, indent=2)}")
                except Exception:
                    print(f"📋 Raw Response: {response.text}")
                return None

        except requests.exceptions.ConnectionError:
            print("❌ CONNECTION ERROR: Could not connect to the API server")
            print("💡 Make sure the API server is running on http://localhost:8000")
            return None
        except requests.exceptions.Timeout:
            print("❌ TIMEOUT ERROR: Request took too long")
            return None
        except Exception as e:
            print(f"❌ UNEXPECTED ERROR: {e}")
            return None

    def next_step(self, run_id: str, current_step: str) -> Optional[Dict[str, Any]]:
        """Trigger the next step with /pipeline/next endpoint."""
        print("\n🔄 Triggering Next Step")
        print(f"🆔 Run ID: {run_id}")
        print(f"📋 Current Step: {current_step}")
        print("-" * 60)

        url = f"{self.api_base}/pipeline/next"
        payload = {"run_id": run_id, "current_step": current_step}

        print(f"📡 POST {url}")
        print(f"📝 Payload: {json.dumps(payload, indent=2)}")

        try:
            response = requests.post(url, json=payload, timeout=30)
            print(f"📊 Status Code: {response.status_code}")

            if response.status_code == 200:
                data = response.json()
                print("✅ SUCCESS: Next step completed")
                print(f"📋 New Step: {data.get('step')}")
                print(f"📄 Data Keys: {list(data.get('data', {}).keys())}")
                return data
            else:
                print(f"❌ FAILED: {response.status_code}")
                try:
                    error_data = response.json()
                    print(f"📋 Error: {json.dumps(error_data, indent=2)}")
                except Exception:
                    print(f"📋 Raw Response: {response.text}")
                return None

        except requests.exceptions.ConnectionError:
            print("❌ CONNECTION ERROR: Could not connect to the API server")
            return None
        except requests.exceptions.Timeout:
            print("❌ TIMEOUT ERROR: Request took too long")
            return None
        except Exception as e:
            print(f"❌ UNEXPECTED ERROR: {e}")
            return None

    def run_complete_pipeline(self, user_idea: str) -> Dict[str, Any]:
        """Run the complete pipeline from start to finish."""
        print("🤖 COMPLETE PIPELINE EXECUTION")
        print("=" * 80)
        print("This demonstrates the exact API calls that would be made")
        print("to run through all pipeline steps.")
        print("=" * 80)

        results = {}

        # Step 1: Start the pipeline
        start_result = self.start_pipeline(user_idea)
        if not start_result:
            print("\n❌ Failed to start pipeline. Stopping execution.")
            return results

        run_id = start_result.get("run_id")
        current_step = start_result.get("step")
        results[current_step] = start_result.get("data")

        # Steps 2-6: Continue through remaining steps
        remaining_steps = PIPELINE_STEPS[
            1:
        ]  # Skip market_research since it's already done

        for next_step_name in remaining_steps:
            print("\n⏳ Waiting 2 seconds before next step...")
            time.sleep(2)

            next_result = self.next_step(run_id, current_step)
            if not next_result:
                print(
                    f"\n❌ Failed to execute step after {current_step}. Stopping execution."
                )
                break

            current_step = next_result.get("step")
            results[current_step] = next_result.get("data")

            # Check if we've reached the end
            if current_step == PIPELINE_STEPS[-1]:
                print(f"\n🎉 Reached final step: {current_step}")
                break

        return results, run_id

    def print_execution_summary(self, results: Dict[str, Any], run_id: str):
        """Print a summary of the pipeline execution."""
        print("\n" + "=" * 80)
        print("📊 PIPELINE EXECUTION SUMMARY")
        print("=" * 80)
        print(f"🆔 Run ID: {run_id}")
        print(f"📈 Steps Completed: {len(results)}/{len(PIPELINE_STEPS)}")

        for i, step in enumerate(PIPELINE_STEPS, 1):
            if step in results:
                print(f"✅ Step {i}: {step.upper().replace('_', ' ')}")
            else:
                print(f"❌ Step {i}: {step.upper().replace('_', ' ')} - Not completed")

        if len(results) == len(PIPELINE_STEPS):
            print("\n🎉 COMPLETE SUCCESS: All pipeline steps executed!")
        else:
            print(
                f"\n⚠️ PARTIAL SUCCESS: {len(results)}/{len(PIPELINE_STEPS)} steps completed"
            )

        print("=" * 80)


def main():
    """Main function to demonstrate pipeline execution."""
    user_idea = "Build a task management app for remote teams"

    # Create pipeline runner
    runner = PipelineRunner()

    # Check if server is running
    try:
        response = requests.get(f"{runner.base_url}/heartbeat", timeout=5)
        if response.status_code == 200:
            print("✅ API server is running")
        else:
            print("⚠️ API server responded but may have issues")
    except Exception:
        print("❌ API server is not running or not accessible")
        print("💡 Start the server with: uv run python main.py")
        print("💡 Or run the simulation instead: python simulate_pipeline.py")
        return

    # Run the complete pipeline
    try:
        results, run_id = runner.run_complete_pipeline(user_idea)
        runner.print_execution_summary(results, run_id)

        # Save results if any were obtained
        if results:
            filename = f"api_pipeline_results_{run_id}.json"
            with open(filename, "w") as f:
                json.dump(results, f, indent=2)
            print(f"\n💾 Results saved to: {filename}")

    except Exception as e:
        print(f"\n❌ Pipeline execution failed: {e}")


if __name__ == "__main__":
    main()
