#!/usr/bin/env python3
"""
Manual test script for the /pipeline/manage-data endpoint.
This script demonstrates how to use the endpoint with various operations.
"""

import asyncio
import json
import sys
import os
from typing import Dict, Any

# Add the project root to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), ".."))

import httpx


class ManageDataManualTest:
    """Manual test class for demonstrating the manage data endpoint."""

    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.test_run_id = "manual-test-run-456"
        self.test_headers = {
            "access-key": "Bearer test-jwt-token",  # Mock JWT token
            "Content-Type": "application/json",
        }

    def print_request(self, operation: str, request_data: Dict[str, Any]):
        """Print the request being made."""
        print(f"📤 {operation} Request:")
        print(f"   URL: {self.base_url}/api/v1/pipeline/manage-data")
        print("   Method: POST")
        print(f"   Headers: {json.dumps(self.test_headers, indent=6)}")
        print(f"   Body: {json.dumps(request_data, indent=6)}")
        print()

    def print_response(
        self, operation: str, status_code: int, response_data: Dict[str, Any]
    ):
        """Print the response received."""
        print(f"📥 {operation} Response:")
        print(f"   Status Code: {status_code}")
        print(f"   Body: {json.dumps(response_data, indent=6)}")
        print()

    async def demonstrate_update_operation(self):
        """Demonstrate the update operation."""
        print("🔄 DEMONSTRATING UPDATE OPERATION")
        print("-" * 50)

        request_data = {
            "run_id": self.test_run_id,
            "step": "market_research",
            "operation_type": "update",
            "payload": {
                "market_summary": "The task management software market is experiencing rapid growth, driven by the increasing adoption of remote work and the need for better collaboration tools. Key trends include AI-powered automation, integration capabilities, and mobile-first design.",
                "identified_gaps": "Current solutions lack real-time collaboration features, have poor mobile experiences, and limited customization options for different team structures.",
            },
        }

        self.print_request("UPDATE", request_data)

        async with httpx.AsyncClient() as client:
            try:
                response = await client.post(
                    f"{self.base_url}/api/v1/pipeline/manage-data",
                    json=request_data,
                    headers=self.test_headers,
                    timeout=30.0,
                )

                response_data = (
                    response.json()
                    if response.status_code != 500
                    else {"error": response.text}
                )
                self.print_response("UPDATE", response.status_code, response_data)

            except Exception as e:
                print(f"❌ Error: {e}")
                print()

    async def demonstrate_add_operation(self):
        """Demonstrate the add operation."""
        print("➕ DEMONSTRATING ADD OPERATION")
        print("-" * 50)

        request_data = {
            "run_id": self.test_run_id,
            "step": "market_research",
            "operation_type": "add",
            "payload": {
                "competitors": [
                    {
                        "name": "Asana",
                        "url": "https://asana.com",
                        "strengths": "Strong project management features, excellent team collaboration tools, robust reporting capabilities",
                    },
                    {
                        "name": "Trello",
                        "url": "https://trello.com",
                        "strengths": "Simple kanban interface, easy to use, good for small teams, extensive Power-Ups ecosystem",
                    },
                    {
                        "name": "Monday.com",
                        "url": "https://monday.com",
                        "strengths": "Highly customizable workflows, great visual project tracking, strong automation features",
                    },
                ]
            },
        }

        self.print_request("ADD", request_data)

        async with httpx.AsyncClient() as client:
            try:
                response = await client.post(
                    f"{self.base_url}/api/v1/pipeline/manage-data",
                    json=request_data,
                    headers=self.test_headers,
                    timeout=30.0,
                )

                response_data = (
                    response.json()
                    if response.status_code != 500
                    else {"error": response.text}
                )
                self.print_response("ADD", response.status_code, response_data)

            except Exception as e:
                print(f"❌ Error: {e}")
                print()

    async def demonstrate_lbc_update(self):
        """Demonstrate updating lean business canvas data."""
        print("📊 DEMONSTRATING LBC UPDATE OPERATION")
        print("-" * 50)

        request_data = {
            "run_id": self.test_run_id,
            "step": "lbc",
            "operation_type": "update",
            "payload": {
                "problem": [
                    "Remote teams struggle with task visibility and accountability",
                    "Existing tools are too complex for small teams",
                    "Poor integration between communication and project management tools",
                ],
                "solution": [
                    "Intuitive task management with real-time collaboration",
                    "Smart notifications and automated progress tracking",
                    "Seamless integration with popular communication tools",
                ],
                "value_proposition": [
                    "Increase team productivity by 40% with better task visibility",
                    "Reduce project delays through automated progress tracking",
                    "Improve team communication with integrated collaboration features",
                ],
                "customer_segments": [
                    "Remote software development teams (5-50 people)",
                    "Digital marketing agencies",
                    "Consulting firms and professional services",
                ],
            },
        }

        self.print_request("LBC UPDATE", request_data)

        async with httpx.AsyncClient() as client:
            try:
                response = await client.post(
                    f"{self.base_url}/api/v1/pipeline/manage-data",
                    json=request_data,
                    headers=self.test_headers,
                    timeout=30.0,
                )

                response_data = (
                    response.json()
                    if response.status_code != 500
                    else {"error": response.text}
                )
                self.print_response("LBC UPDATE", response.status_code, response_data)

            except Exception as e:
                print(f"❌ Error: {e}")
                print()

    async def demonstrate_delete_operation(self):
        """Demonstrate the delete operation."""
        print("🗑️  DEMONSTRATING DELETE OPERATION")
        print("-" * 50)

        request_data = {
            "run_id": self.test_run_id,
            "step": "market_research",
            "operation_type": "delete",
            "payload": {
                "competitors": [
                    {
                        "name": "Trello",
                        "url": "https://trello.com",
                        "strengths": "Simple kanban interface, easy to use, good for small teams, extensive Power-Ups ecosystem",
                    }
                ]
            },
        }

        self.print_request("DELETE", request_data)

        async with httpx.AsyncClient() as client:
            try:
                response = await client.post(
                    f"{self.base_url}/api/v1/pipeline/manage-data",
                    json=request_data,
                    headers=self.test_headers,
                    timeout=30.0,
                )

                response_data = (
                    response.json()
                    if response.status_code != 500
                    else {"error": response.text}
                )
                self.print_response("DELETE", response.status_code, response_data)

            except Exception as e:
                print(f"❌ Error: {e}")
                print()

    async def demonstrate_error_cases(self):
        """Demonstrate error cases."""
        print("⚠️  DEMONSTRATING ERROR CASES")
        print("-" * 50)

        # Invalid step
        print("1. Invalid Step Name:")
        invalid_step_request = {
            "run_id": self.test_run_id,
            "step": "invalid_step",
            "operation_type": "update",
            "payload": {"test": "data"},
        }

        self.print_request("INVALID STEP", invalid_step_request)

        async with httpx.AsyncClient() as client:
            try:
                response = await client.post(
                    f"{self.base_url}/api/v1/pipeline/manage-data",
                    json=invalid_step_request,
                    headers=self.test_headers,
                    timeout=30.0,
                )

                response_data = (
                    response.json()
                    if response.status_code != 500
                    else {"error": response.text}
                )
                self.print_response("INVALID STEP", response.status_code, response_data)

            except Exception as e:
                print(f"❌ Error: {e}")
                print()

        # Invalid operation type
        print("2. Invalid Operation Type:")
        invalid_op_request = {
            "run_id": self.test_run_id,
            "step": "market_research",
            "operation_type": "invalid_operation",
            "payload": {"test": "data"},
        }

        self.print_request("INVALID OPERATION", invalid_op_request)

        async with httpx.AsyncClient() as client:
            try:
                response = await client.post(
                    f"{self.base_url}/api/v1/pipeline/manage-data",
                    json=invalid_op_request,
                    headers=self.test_headers,
                    timeout=30.0,
                )

                response_data = (
                    response.json()
                    if response.status_code != 500
                    else {"error": response.text}
                )
                self.print_response(
                    "INVALID OPERATION", response.status_code, response_data
                )

            except Exception as e:
                print(f"❌ Error: {e}")
                print()

    async def run_demonstration(self):
        """Run the complete demonstration."""
        print("🎯 MANUAL TEST DEMONSTRATION: /pipeline/manage-data Endpoint")
        print("=" * 80)
        print(f"Base URL: {self.base_url}")
        print(f"Test Run ID: {self.test_run_id}")
        print("=" * 80)
        print()

        # Check if server is available
        async with httpx.AsyncClient() as client:
            try:
                response = await client.get(f"{self.base_url}/heartbeat")
                if response.status_code != 200:
                    print("❌ Server is not available. Please start the server first.")
                    return
            except Exception as e:
                print(f"❌ Server is not available: {e}")
                return

        print("✅ Server is available")
        print()

        # Run demonstrations
        await self.demonstrate_update_operation()
        await self.demonstrate_add_operation()
        await self.demonstrate_lbc_update()
        await self.demonstrate_delete_operation()
        await self.demonstrate_error_cases()

        print("=" * 80)
        print("🎉 Manual test demonstration completed!")
        print("=" * 80)


async def main():
    """Main function to run the manual test demonstration."""
    import argparse

    parser = argparse.ArgumentParser(
        description="Manual test demonstration for /pipeline/manage-data endpoint"
    )
    parser.add_argument(
        "--url", default="http://localhost:8000", help="Base URL of the API server"
    )
    args = parser.parse_args()

    tester = ManageDataManualTest(base_url=args.url)
    await tester.run_demonstration()


if __name__ == "__main__":
    asyncio.run(main())
