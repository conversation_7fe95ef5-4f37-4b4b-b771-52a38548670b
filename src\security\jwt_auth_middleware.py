"""
JWT Authentication Middleware for FastAPI

This middleware intercepts all incoming requests, checks for access-key header,
skips authentication for public routes, and validates JWT tokens for protected routes.
"""

from typing import Set
from fastapi import Request, HTTPException
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware

from src.security.jwt_decoder import decode_jwt_and_extract_user_details
from src.utils.logger import AppLogger

logger = AppLogger(__name__).get_logger()


class JWTAuthMiddleware(BaseHTTPMiddleware):
    """
    JWT Authentication Middleware that validates JWT tokens and user existence.
    """

    def __init__(self, app, public_routes: Set[str] = None):
        """
        Initialize the JWT authentication middleware.

        Args:
            app: FastAPI application instance
            public_routes: Set of route paths that don't require authentication
        """
        super().__init__(app)

        # Default public routes that don't require authentication
        self.public_routes = public_routes or {
            "/server/product/",
            "/health",
            "/heartbeat",
            "/server/product/docs",
            "/server/product/redoc",
            "/redoc",
            "/openapi.json",
            "/server/product/openapi.json",
            "/status",
            "/static",
        }

        # Add common variations
        self.public_routes.update(
            {
                "/docs/",
                "/redoc/",
                "/openapi.json/",
                "/status/",
                "/health/",
                "/heartbeat/",
            }
        )

        # Add routes that should remain unprotected based on user requirements
        # Common routes (from routes.py)
        self.public_routes.update(
            {"/enhance", "/upload-image/", "/user", "/reload-settings"}
        )

        # Design analysis route (should not require access-key)
        self.public_routes.add("/design-analysis/design-analysis/design/analyse")

        # Wireframe intro route (should not require access-key)
        self.public_routes.add("/wireframe-generation/intro")

    def _is_public_route(self, path: str) -> bool:
        """
        Check if the given path is a public route that doesn't require authentication.

        Args:
            path: Request path

        Returns:
            bool: True if the route is public, False otherwise
        """
        # Exact match
        if path in self.public_routes:
            return True

        # Check for static files and other patterns
        if path.startswith("/static/"):
            return True

        if path.startswith("/docs"):
            return True

        if path.startswith("/redoc"):
            return True

        # Check for OpenAPI related paths
        if "openapi" in path.lower():
            return True

        # Check for streaming routes (SSE endpoints)
        if path.startswith("/project-status/"):
            return True

        if path == "/sse-diagnostics":
            return True

        # Check for download-image routes with dynamic filename
        if path.startswith("/download-image/"):
            return True

        return False

    async def dispatch(self, request: Request, call_next):
        """
        Process the request through JWT authentication middleware.

        Args:
            request: FastAPI Request object
            call_next: Next middleware/route handler in the chain

        Returns:
            Response: HTTP response
        """
        path = request.url.path
        method = request.method

        logger.debug(f"Processing request: {method} {path}")

        # Skip authentication for public routes
        if self._is_public_route(path):
            logger.debug(f"Skipping authentication for public route: {path}")
            return await call_next(request)

        # Skip authentication for OPTIONS requests (CORS preflight)
        if method == "OPTIONS":
            logger.debug("Skipping authentication for OPTIONS request")
            return await call_next(request)

        try:
            # Extract and decode JWT token to get complete user details
            user_details = decode_jwt_and_extract_user_details(request)
            email = user_details.get("email")
            logger.info(f"JWT decoded successfully for email: {email}")

            # Store complete user details in request state for use by route handler dependencies
            # The actual user validation and data fetching will be done by get_authenticated_user dependency
            request.state.user_email = email
            request.state.user_details = user_details

            logger.debug(f"User details set in request.state for email: {email}")

        except HTTPException as e:
            # JWT decoding failed
            logger.warning(f"JWT authentication failed for {path}: {e.detail}")

            return JSONResponse(
                status_code=e.status_code,
                content={"detail": e.detail, "error": "JWT authentication failed"},
            )

        except Exception as e:
            # Unexpected error during authentication
            logger.error(f"Unexpected authentication error for {path}: {str(e)}")

            return JSONResponse(
                status_code=500,
                content={
                    "detail": "Internal server error during authentication",
                    "error": "Authentication error",
                },
            )

        # Continue to the next middleware/route handler
        try:
            response = await call_next(request)
            return response

        except Exception as e:
            logger.error(f"Error in downstream handler for {path}: {str(e)}")
            return JSONResponse(
                status_code=500,
                content={"detail": "Internal server error", "error": str(e)},
            )


def create_jwt_auth_middleware(public_routes: Set[str] = None) -> JWTAuthMiddleware:
    """
    Factory function to create JWT authentication middleware with custom public routes.

    Args:
        public_routes: Set of additional public routes that don't require authentication

    Returns:
        JWTAuthMiddleware: Configured middleware instance
    """
    return JWTAuthMiddleware(None, public_routes)


# Default middleware instance
jwt_auth_middleware = JWTAuthMiddleware(None)
