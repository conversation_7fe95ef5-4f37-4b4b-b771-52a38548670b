FROM python:3.13.7-slim

# Set the working directory in the container
COPY --from=ghcr.io/astral-sh/uv:latest /uv /uvx /bin/

# Copy project files
COPY . /app

# Sync the project into a new environment, asserting the lockfile is up to date
WORKDIR /app
RUN uv sync --frozen --no-cache --no-dev

# Expose the port the application runs on
EXPOSE 8000

# Create a non-root user and switch to it
#RUN adduser -D michelangelo
RUN useradd -m -u 10001 michelangelo
# Set environment variables for better Docker behavior
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1

# Switch to non-root user
USER michelangelo

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:8000/heartbeat || exit 1

CMD ["/app/.venv/bin/uvicorn", "src.main:app", "--port", "8000", "--host", "0.0.0.0", "--log-level", "info"]
