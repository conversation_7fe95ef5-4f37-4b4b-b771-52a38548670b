from src.config.brainstormer_prompt import (
    BRAINSTORMER_CONVERSATIONAL_AGENT_PROMPT,
    MARKET_RESEARCH_PROMPT,
    LBC_PROMPT,
    PERSONA_PROMPT,
    SWOT_PROMPT,
    FEATURE_PROMPT,
    ROADMAP_PROMPT,
    REGENERATE_PROMPT,
)

# In config.py
PIPELINE_STEPS = ["market_research", "lbc", "persona", "swot", "features", "roadmap"]

# Defines what context is needed for each step's prompt
PIPELINE_CONTEXT_DEFINITION = {
    "market_research": {
        "context_needed": []
    },  # No context needed, user_request is passed directly
    "lbc": {
        "context_needed": ["market_research"]
    },  # user_request will be passed directly via additional_context
    "persona": {
        "context_needed": ["lbc"],
    },
    "swot": {
        "context_needed": ["market_research", "lbc", "persona"],
    },
    "features": {
        "context_needed": ["swot", "persona", "lbc"],
    },
    "roadmap": {"context_needed": ["swot", "features"]},
}

AGENT_NAMES = {
    "market_research": {
        "name": "EE_PS_MARKET_RESEARCH_TOOL_AGENT",
        "prompt": MARKET_RESEARCH_PROMPT,
    },
    "lbc": {"name": "EE_PS_BRAINSTORMER_LBC", "prompt": LBC_PROMPT},
    "persona": {"name": "EE_PS_BRAINSTORMER_PERSONA", "prompt": PERSONA_PROMPT},
    "swot": {"name": "EE_PS_BRAINSTORMER_SWOT", "prompt": SWOT_PROMPT},
    "features": {"name": "EE_PS_BRAINSTORMER_FEATURES", "prompt": FEATURE_PROMPT},
    "roadmap": {"name": "EE_PS_BRAINSTORMER_ROADMAP", "prompt": ROADMAP_PROMPT},
    "conversational": {
        "name": "EE_PS_BRAINSTORMER_CONVERSATIONAL_AGENT",
        "prompt": BRAINSTORMER_CONVERSATIONAL_AGENT_PROMPT,
    },
    "regenerate": {
        "name": "EE_PS_BRAINSTORMER_REGENERATE",
        "prompt": REGENERATE_PROMPT,
    },
}
