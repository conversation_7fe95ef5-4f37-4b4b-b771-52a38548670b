"""
JWT Decoding Utility for FastAPI Authentication

Simple JWT decoder that extracts and decodes JWT tokens from the access-key header.
"""

import base64
import json
from fastapi import HTTPException, Request
import jwt
from src.security.user_details_decryptor import get_decryptor
from src.utils.logger import AppLogger

logger = AppLogger(__name__).get_logger()


def decode_base64url(data):
    """Decode base64url data with proper padding"""
    padded = data + "=" * (-len(data) % 4)  # Fix padding
    return json.loads(base64.urlsafe_b64decode(padded).decode("utf-8"))


def decode_jwt_and_extract_user_details(request: Request) -> dict:
    """
    Extract JWT from authorization header, decode it, and decrypt user details.

    Args:
        request: FastAPI Request object

    Returns:
        dict: Complete user details from JWT payload

    Raises:
        HTTPException: With appropriate status codes for different error types
    """
    try:
        # Extract JWT from authorization header (new format)
        auth_header = request.headers.get("authorization")
        if not auth_header or not auth_header.strip():
            # Fallback to access-key header for backward compatibility
            access_key = request.headers.get("access-key")
            if not access_key or not access_key.strip():
                raise HTTPException(
                status_code=401, detail="Missing authorization header. Please provide a valid Bearer token."
                )

            jwt_token = access_key.strip()
        else:
            jwt_token = auth_header.strip()

        # Ensure it's a Bearer token
        if not jwt_token.startswith("Bearer "):
            # If it doesn't start with Bearer, assume it's the token itself
            jwt_token = f"Bearer {jwt_token}"
        try:
            # Get the decryptor instance
            decryptor = get_decryptor()
            
            # Extract complete user information using the decryptor
            complete_user_info = decryptor.get_complete_user_info(jwt_token)
            
            logger.info(f"Successfully extracted and decrypted user details for email: {complete_user_info.get('email')}")
            return complete_user_info
            
        except Exception as decryption_error:
            logger.warning(f"Failed to decrypt user details, falling back to basic JWT parsing: {str(decryption_error)}")
            
        #jwt_token = access_key.strip()
            # Fallback to basic JWT parsing if decryption fails
            # Remove 'Bearer ' prefix for JWT parsing
            clean_token = jwt_token[7:] if jwt_token.startswith("Bearer ") else jwt_token
        # Split JWT into parts (header.payload.signature)
            parts = clean_token.split('.')
            if len(parts) != 3:
                raise HTTPException(
                    status_code=401,
                    detail=f"Invalid JWT format: expected 3 parts, got {len(parts)}",
                )

        # Decode the payload (second part)
        payload = decode_base64url(parts[1])
        
        
        # Extract email from payload - check multiple possible fields
        email = (
            payload.get("email")
            or payload.get("unique_name")
            or payload.get("upn")
            or payload.get("preferred_username")
        )

        if not email:
            logger.warning(
                f"Email not found in JWT payload. Available fields: {list(payload.keys())}"
            )
            raise HTTPException(
                status_code=401, detail="Email field not found in JWT payload"
            )

        if not isinstance(email, str) or not email.strip() or "@" not in email:
            raise HTTPException(
                status_code=401, detail="Invalid email format in JWT payload"
            )

        email = email.strip()

        # # Extract userDetails from payload
        # user_details = payload.get("userDetails", {})

        # if not user_details:
        #     logger.warning(
        #         f"userDetails not found in JWT payload. Available fields: {list(payload.keys())}"
        #     )
        #     # Fallback to basic user info if userDetails is not available
        #     user_details = {
        #         "userName": payload.get("name", ""),
        #         "email": email,
        #         "realms": [],
        #         "roles": [],
        #     }
        # Return basic user info as fallback
        return {
            "userId": "",  # Will be set by get_authenticated_user
            "email": email,
            "userName": payload.get('name', ''),
            #"email": email,
            "realms": [],
            "roles": []
        }
        # # Ensure email is included in userDetails
        # user_details["email"] = email

        # logger.info(f"Successfully extracted user details from JWT for email: {email}")
        # return user_details

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error processing JWT: {str(e)}")
        raise HTTPException(status_code=401, detail=f"Invalid JWT token: {str(e)}")
