-- Create schema for aava_prod_studio data
CREATE SCHEMA IF NOT EXISTS aava_prod_studio;


-- Main Projects table
CREATE TABLE aava_prod_studio.projects (
    id SERIAL PRIMARY KEY,
    run_id UUID UNIQUE NOT NULL,
    name VA<PERSON>HA<PERSON>(255) NOT NULL,
    description TEXT,
    created_by INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'completed', 'archived', 'draft')),
    tags JSONB DEFAULT '[]',
    metadata JSONB DEFAULT '{}',
    FOREIGN KEY (created_by) REFERENCES rbac.users(user_id) ON DELETE RESTRICT
);

-- Step 0: Market Research
CREATE TABLE aava_prod_studio.market_research (
    id SERIAL PRIMARY KEY,
    project_id INTEGER NOT NULL UNIQUE,
    market_summary TEXT NOT NULL,
    identified_gaps TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES aava_prod_studio.projects(id) ON DELETE CASCADE
);

CREATE TABLE aava_prod_studio.competitors (
    id SERIAL PRIMARY KEY,
    market_research_id INTEGER NOT NULL,
    name VARCHAR(255) NOT NULL,
    url TEXT,
    strengths TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (market_research_id) REFERENCES aava_prod_studio.market_research(id) ON DELETE CASCADE
);

-- Step 1: Lean Business Canvas
CREATE TABLE aava_prod_studio.lean_business_canvas (
    id SERIAL PRIMARY KEY,
    project_id INTEGER NOT NULL UNIQUE,
    problem JSONB DEFAULT '[]',
    solution JSONB DEFAULT '[]',
    key_partners JSONB DEFAULT '[]',
    value_proposition JSONB DEFAULT '[]',
    customer_segments JSONB DEFAULT '[]',
    revenue_streams JSONB DEFAULT '[]',
    key_metrics JSONB DEFAULT '[]',
    alternatives JSONB DEFAULT '[]',
    solution_tenants JSONB DEFAULT '[]',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES aava_prod_studio.projects(id) ON DELETE CASCADE
);

-- Step 2: User Personas
CREATE TABLE aava_prod_studio.user_personas (
    id SERIAL PRIMARY KEY,
    project_id INTEGER NOT NULL,
    name VARCHAR(255) NOT NULL,
    role VARCHAR(255),
    age INTEGER,
    gender VARCHAR(20) CHECK (gender IN ('male', 'female', 'non-binary')),
    education VARCHAR(255),
    status VARCHAR(255),
    location VARCHAR(255),
    tech_literacy VARCHAR(255),
    avatar TEXT,
    quote TEXT,
    personality JSONB DEFAULT '[]',
    pain_points JSONB DEFAULT '[]',
    goals JSONB DEFAULT '[]',
    motivation JSONB DEFAULT '[]',
    expectations JSONB DEFAULT '[]',
    devices JSONB DEFAULT '[]',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES aava_prod_studio.projects(id) ON DELETE CASCADE
);

-- Step 3 & 4: SWOT Analysis
CREATE TABLE aava_prod_studio.swot_analysis (
    id SERIAL PRIMARY KEY,
    project_id INTEGER NOT NULL UNIQUE,
    strengths JSONB DEFAULT '[]',
    weaknesses JSONB DEFAULT '[]',
    opportunities JSONB DEFAULT '[]',
    threats JSONB DEFAULT '[]',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES aava_prod_studio.projects(id) ON DELETE CASCADE
);

-- Features, Roadmap tasks, etc. all reference project_id
CREATE TABLE aava_prod_studio.features (
    id SERIAL PRIMARY KEY,
    project_id INTEGER NOT NULL,
    category VARCHAR(20) CHECK (category IN ('must_have', 'should_have', 'could_have', 'wont_have')),
    title VARCHAR(255) NOT NULL,
    description TEXT,
    tenant VARCHAR(255),
    justification TEXT,
    tags JSONB DEFAULT '[]',
    moscow_rank VARCHAR(20) CHECK (moscow_rank IN ('Must-Have', 'Should-Have', 'Could-Have', 'Won''t-Have')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES aava_prod_studio.projects(id) ON DELETE CASCADE
);

-- Step 5: Roadmap
CREATE TABLE aava_prod_studio.roadmap_tasks (
    id SERIAL PRIMARY KEY,
    project_id INTEGER NOT NULL,
    task VARCHAR(255) NOT NULL,
    description TEXT,
    long_description TEXT,
    priority VARCHAR(10) CHECK (priority IN ('low', 'medium', 'high')),
    duration INTEGER,
    quarter INTEGER CHECK (quarter >= 1 AND quarter <= 4),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES aava_prod_studio.projects(id) ON DELETE CASCADE
);

-- Project Conversations - separate table approach
CREATE TABLE aava_prod_studio.project_conversations (
    id SERIAL PRIMARY KEY,
    project_id INTEGER NOT NULL,
    conversation_type VARCHAR(50) CHECK (conversation_type IN ('market_research', 'lean_business_canvas', 'user_personas', 'swot_analysis', 'features', 'roadmap_tasks', 'competitors')),
    content TEXT NOT NULL,
    role VARCHAR(20) CHECK (role IN ('system', 'user', 'assistant')),
    message_order INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES aava_prod_studio.projects(id) ON DELETE CASCADE
);

-- Add indexes for conversations
CREATE INDEX idx_project_conversations_project_id ON aava_prod_studio.project_conversations(project_id);
CREATE INDEX idx_project_conversations_type ON aava_prod_studio.project_conversations(conversation_type);
CREATE INDEX idx_project_conversations_order ON aava_prod_studio.project_conversations(project_id, conversation_type, message_order);

-- Create indexes
CREATE INDEX idx_projects_run_id ON aava_prod_studio.projects(run_id);
CREATE INDEX idx_projects_created_by ON aava_prod_studio.projects(created_by);
CREATE INDEX idx_projects_status ON aava_prod_studio.projects(status);
CREATE INDEX idx_market_research_project_id ON aava_prod_studio.market_research(project_id);
CREATE INDEX idx_lbc_project_id ON aava_prod_studio.lean_business_canvas(project_id);
CREATE INDEX idx_personas_project_id ON aava_prod_studio.user_personas(project_id);
CREATE INDEX idx_swot_analysis_project_id ON aava_prod_studio.swot_analysis(project_id);
CREATE INDEX idx_features_project_id ON aava_prod_studio.features(project_id);
CREATE INDEX idx_roadmap_tasks_project_id ON aava_prod_studio.roadmap_tasks(project_id);
