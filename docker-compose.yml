version: '3.8'

services:
  app:
    build: .
    ports:
      - "8000:8000"
    environment:
      # Database Configuration - Override these in production
      - DB_HOST=${DB_HOST:-localhost}
      - DB_PORT=${DB_PORT:-5432}
      - DB_USER=${DB_USER:-postgres}
      - DB_PASSWORD=${DB_PASSWORD:-postgres}
      - DB_NAME=${DB_NAME:-product_studio}

      # Redis Configuration
      - REDIS_URL=${REDIS_URL:-redis://redis:6379/0}

      # API Configuration
      - BASE_URL=${BASE_URL:-https://avaplus-internal.avateam.io}
      - PIPELINE_AGENT_SERVICE_ENDPOINT=${PIPELINE_AGENT_SERVICE_ENDPOINT:-v1/api/instructions/ava/force/workflow-executions}
      - INDIVIDUAL_AGENT_SERVICE_ENDPOINT=${INDIVIDUAL_AGENT_SERVICE_ENDPOINT:-v1/api/instructions/ava/force/individualAgent/execute}

      # Access Key
      - ACCESS_KEY=${ACCESS_KEY}

      # Application Configuration
      - DEBUG=${DEBUG:-false}
      - APP_NAME=${APP_NAME:-Product Studio Server}

      # Kubernetes/Proxy Configuration
      - ROOT_PATH=${ROOT_PATH:-}
    depends_on:
      - redis
    networks:
      - app-network

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    networks:
      - app-network

networks:
  app-network:
    driver: bridge
