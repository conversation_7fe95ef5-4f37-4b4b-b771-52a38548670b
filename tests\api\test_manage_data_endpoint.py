"""
Tests for the /pipeline/manage-data endpoint.
"""

import pytest
from unittest.mock import patch, AsyncMock
from fastapi import status


class TestManageDataEndpoint:
    """Test class for the pipeline data management endpoint."""

    @pytest.fixture
    def manage_data_test_data(self):
        """Test data for manage data endpoint."""
        return {
            "update_request": {
                "run_id": "test-run-id-123",
                "step": "market_research",
                "operation_type": "update",
                "payload": {
                    "market_summary": "Updated market summary with new insights",
                    "identified_gaps": "Updated gaps analysis",
                },
            },
            "add_request": {
                "run_id": "test-run-id-123",
                "step": "market_research",
                "operation_type": "add",
                "payload": {
                    "competitors": [
                        {
                            "name": "New Competitor",
                            "url": "https://newcompetitor.com",
                            "strengths": "Innovative features",
                        }
                    ]
                },
            },
            "delete_request": {
                "run_id": "test-run-id-123",
                "step": "market_research",
                "operation_type": "delete",
                "payload": {
                    "competitors": [
                        {
                            "name": "Old Competitor",
                            "url": "https://oldcompetitor.com",
                            "strengths": "Legacy system",
                        }
                    ]
                },
            },
            "lbc_update_request": {
                "run_id": "test-run-id-456",
                "step": "lbc",
                "operation_type": "update",
                "payload": {
                    "problem": ["Updated problem statement"],
                    "solution": ["Updated solution approach"],
                },
            },
            "features_add_request": {
                "run_id": "test-run-id-789",
                "step": "features",
                "operation_type": "add",
                "payload": {
                    "must_have": [
                        {
                            "title": "New Must-Have Feature",
                            "description": "Critical feature for success",
                            "tenant": "Core",
                            "justification": "Essential for MVP",
                            "tags": ["core", "mvp"],
                            "moscow_rank": "Must-Have",
                        }
                    ]
                },
            },
        }

    @patch("src.routes.brainstormer_route.get_pipeline_manager")
    async def test_manage_data_update_success(
        self, mock_get_pipeline_manager, async_test_client, manage_data_test_data
    ):
        """Test successful data update operation."""
        # Setup mock
        mock_pipeline_manager = AsyncMock()
        expected_response = {
            "success": True,
            "message": "Successfully updated data for step market_research",
            "data": {
                "market_summary": "Updated market summary with new insights",
                "identified_gaps": "Updated gaps analysis",
                "competitors": [],
            },
        }
        mock_pipeline_manager.manage_step_data.return_value = expected_response
        mock_get_pipeline_manager.return_value = mock_pipeline_manager

        # Make request
        response = await async_test_client.post(
            "/api/v1/pipeline/manage-data", json=manage_data_test_data["update_request"]
        )

        # Assertions
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert response_data["success"] is True
        assert "Successfully updated data" in response_data["message"]
        assert "data" in response_data

        # Verify mock was called correctly
        mock_pipeline_manager.manage_step_data.assert_called_once()
        call_args = mock_pipeline_manager.manage_step_data.call_args
        assert (
            call_args[1]["run_id"] == manage_data_test_data["update_request"]["run_id"]
        )
        assert call_args[1]["step"] == manage_data_test_data["update_request"]["step"]
        assert call_args[1]["operation_type"] == "update"
        assert call_args[1]["user_signature"] == "<EMAIL>"

    @patch("src.routes.brainstormer_route.get_pipeline_manager")
    async def test_manage_data_add_success(
        self, mock_get_pipeline_manager, async_test_client, manage_data_test_data
    ):
        """Test successful data add operation."""
        # Setup mock
        mock_pipeline_manager = AsyncMock()
        expected_response = {
            "success": True,
            "message": "Successfully added data for step market_research",
            "data": {
                "market_summary": "Existing summary",
                "competitors": [
                    {
                        "name": "New Competitor",
                        "url": "https://newcompetitor.com",
                        "strengths": "Innovative features",
                    }
                ],
            },
        }
        mock_pipeline_manager.manage_step_data.return_value = expected_response
        mock_get_pipeline_manager.return_value = mock_pipeline_manager

        # Make request
        response = await async_test_client.post(
            "/api/v1/pipeline/manage-data", json=manage_data_test_data["add_request"]
        )

        # Assertions
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert response_data["success"] is True
        assert "Successfully added data" in response_data["message"]
        assert len(response_data["data"]["competitors"]) == 1

        # Verify mock was called correctly
        mock_pipeline_manager.manage_step_data.assert_called_once()
        call_args = mock_pipeline_manager.manage_step_data.call_args
        assert call_args[1]["operation_type"] == "add"

    @patch("src.routes.brainstormer_route.get_pipeline_manager")
    async def test_manage_data_delete_success(
        self, mock_get_pipeline_manager, async_test_client, manage_data_test_data
    ):
        """Test successful data delete operation."""
        # Setup mock
        mock_pipeline_manager = AsyncMock()
        expected_response = {
            "success": True,
            "message": "Successfully deleted data for step market_research",
            "data": {
                "market_summary": "Existing summary",
                "competitors": [],  # Competitor was deleted
            },
        }
        mock_pipeline_manager.manage_step_data.return_value = expected_response
        mock_get_pipeline_manager.return_value = mock_pipeline_manager

        # Make request
        response = await async_test_client.post(
            "/api/v1/pipeline/manage-data", json=manage_data_test_data["delete_request"]
        )

        # Assertions
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert response_data["success"] is True
        assert "Successfully deleted data" in response_data["message"]
        assert len(response_data["data"]["competitors"]) == 0

        # Verify mock was called correctly
        mock_pipeline_manager.manage_step_data.assert_called_once()
        call_args = mock_pipeline_manager.manage_step_data.call_args
        assert call_args[1]["operation_type"] == "delete"

    @patch("src.routes.brainstormer_route.get_pipeline_manager")
    async def test_manage_data_invalid_step(
        self, mock_get_pipeline_manager, async_test_client, manage_data_test_data
    ):
        """Test manage data with invalid step name."""
        # Setup mock to raise ValueError
        mock_pipeline_manager = AsyncMock()
        mock_pipeline_manager.manage_step_data.side_effect = ValueError(
            "Invalid step name: invalid_step. Valid steps are: ['market_research', 'lbc', 'persona', 'swot', 'features', 'roadmap']"
        )
        mock_get_pipeline_manager.return_value = mock_pipeline_manager

        invalid_request = manage_data_test_data["update_request"].copy()
        invalid_request["step"] = "invalid_step"

        response = await async_test_client.post(
            "/api/v1/pipeline/manage-data", json=invalid_request
        )

        assert response.status_code == status.HTTP_400_BAD_REQUEST
        response_data = response.json()
        assert "Invalid step name" in response_data["detail"]

    @patch("src.routes.brainstormer_route.get_pipeline_manager")
    async def test_manage_data_invalid_operation_type(
        self, mock_get_pipeline_manager, async_test_client, manage_data_test_data
    ):
        """Test manage data with invalid operation type."""
        # Setup mock to raise ValueError
        mock_pipeline_manager = AsyncMock()
        mock_pipeline_manager.manage_step_data.side_effect = ValueError(
            "Invalid operation type: invalid_op. Valid types are: update, delete, add"
        )
        mock_get_pipeline_manager.return_value = mock_pipeline_manager

        invalid_request = manage_data_test_data["update_request"].copy()
        invalid_request["operation_type"] = "invalid_op"

        response = await async_test_client.post(
            "/api/v1/pipeline/manage-data", json=invalid_request
        )

        assert response.status_code == status.HTTP_400_BAD_REQUEST
        response_data = response.json()
        assert "Invalid operation type" in response_data["detail"]

    async def test_manage_data_missing_required_fields(self, async_test_client):
        """Test manage data with missing required fields."""
        incomplete_request = {
            "run_id": "test-run-id-123",
            # Missing step, operation_type, and payload
        }

        response = await async_test_client.post(
            "/api/v1/pipeline/manage-data", json=incomplete_request
        )

        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        response_data = response.json()
        assert "detail" in response_data

    @patch("src.routes.brainstormer_route.get_pipeline_manager")
    async def test_manage_data_internal_error(
        self, mock_get_pipeline_manager, async_test_client, manage_data_test_data
    ):
        """Test manage data with internal server error."""
        # Setup mock to raise generic exception
        mock_pipeline_manager = AsyncMock()
        mock_pipeline_manager.manage_step_data.side_effect = Exception(
            "Database connection failed"
        )
        mock_get_pipeline_manager.return_value = mock_pipeline_manager

        response = await async_test_client.post(
            "/api/v1/pipeline/manage-data", json=manage_data_test_data["update_request"]
        )

        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        response_data = response.json()
        assert "detail" in response_data
        assert "Database connection failed" in response_data["detail"]

    @patch("src.routes.brainstormer_route.get_pipeline_manager")
    async def test_manage_data_lbc_step(
        self, mock_get_pipeline_manager, async_test_client, manage_data_test_data
    ):
        """Test manage data for lean business canvas step."""
        # Setup mock
        mock_pipeline_manager = AsyncMock()
        expected_response = {
            "success": True,
            "message": "Successfully updated data for step lbc",
            "data": {
                "problem": ["Updated problem statement"],
                "solution": ["Updated solution approach"],
                "key_partners": ["Partner 1"],
                "value_proposition": ["Value 1"],
            },
        }
        mock_pipeline_manager.manage_step_data.return_value = expected_response
        mock_get_pipeline_manager.return_value = mock_pipeline_manager

        # Make request
        response = await async_test_client.post(
            "/api/v1/pipeline/manage-data",
            json=manage_data_test_data["lbc_update_request"],
        )

        # Assertions
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert response_data["success"] is True
        assert response_data["data"]["problem"] == ["Updated problem statement"]
        assert response_data["data"]["solution"] == ["Updated solution approach"]

        # Verify mock was called correctly
        mock_pipeline_manager.manage_step_data.assert_called_once()
        call_args = mock_pipeline_manager.manage_step_data.call_args
        assert call_args[1]["step"] == "lbc"

    @patch("src.routes.brainstormer_route.get_pipeline_manager")
    async def test_manage_data_features_step(
        self, mock_get_pipeline_manager, async_test_client, manage_data_test_data
    ):
        """Test manage data for features step."""
        # Setup mock
        mock_pipeline_manager = AsyncMock()
        expected_response = {
            "success": True,
            "message": "Successfully added data for step features",
            "data": {
                "must_have": [
                    {
                        "title": "New Must-Have Feature",
                        "description": "Critical feature for success",
                        "tenant": "Core",
                        "justification": "Essential for MVP",
                        "tags": ["core", "mvp"],
                        "moscow_rank": "Must-Have",
                    }
                ],
                "should_have": [],
                "could_have": [],
                "wont_have": [],
            },
        }
        mock_pipeline_manager.manage_step_data.return_value = expected_response
        mock_get_pipeline_manager.return_value = mock_pipeline_manager

        # Make request
        response = await async_test_client.post(
            "/api/v1/pipeline/manage-data",
            json=manage_data_test_data["features_add_request"],
        )

        # Assertions
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert response_data["success"] is True
        assert len(response_data["data"]["must_have"]) == 1
        assert response_data["data"]["must_have"][0]["title"] == "New Must-Have Feature"

        # Verify mock was called correctly
        mock_pipeline_manager.manage_step_data.assert_called_once()
        call_args = mock_pipeline_manager.manage_step_data.call_args
        assert call_args[1]["step"] == "features"
        assert call_args[1]["operation_type"] == "add"

    @patch("src.routes.brainstormer_route.get_pipeline_manager")
    async def test_manage_data_empty_payload(
        self, mock_get_pipeline_manager, async_test_client
    ):
        """Test manage data with empty payload."""
        # Setup mock
        mock_pipeline_manager = AsyncMock()
        expected_response = {
            "success": True,
            "message": "Successfully updated data for step market_research",
            "data": {"market_summary": "Existing summary", "competitors": []},
        }
        mock_pipeline_manager.manage_step_data.return_value = expected_response
        mock_get_pipeline_manager.return_value = mock_pipeline_manager

        empty_payload_request = {
            "run_id": "test-run-id-123",
            "step": "market_research",
            "operation_type": "update",
            "payload": {},
        }

        # Make request
        response = await async_test_client.post(
            "/api/v1/pipeline/manage-data", json=empty_payload_request
        )

        # Assertions
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert response_data["success"] is True

        # Verify mock was called correctly
        mock_pipeline_manager.manage_step_data.assert_called_once()
        call_args = mock_pipeline_manager.manage_step_data.call_args
        assert call_args[1]["payload"] == {}

    @patch("src.routes.brainstormer_route.get_pipeline_manager")
    async def test_manage_data_operation_failure(
        self, mock_get_pipeline_manager, async_test_client, manage_data_test_data
    ):
        """Test manage data when operation fails but doesn't raise exception."""
        # Setup mock to return failure response
        mock_pipeline_manager = AsyncMock()
        failure_response = {
            "success": False,
            "message": "Failed to update data: Data validation error",
            "data": None,
        }
        mock_pipeline_manager.manage_step_data.return_value = failure_response
        mock_get_pipeline_manager.return_value = mock_pipeline_manager

        # Make request
        response = await async_test_client.post(
            "/api/v1/pipeline/manage-data", json=manage_data_test_data["update_request"]
        )

        # Assertions
        assert (
            response.status_code == status.HTTP_200_OK
        )  # Still 200 because no exception
        response_data = response.json()
        assert response_data["success"] is False
        assert "Failed to update data" in response_data["message"]
        assert response_data["data"] is None

    async def test_manage_data_invalid_json(self, async_test_client):
        """Test manage data with invalid JSON payload."""
        response = await async_test_client.post(
            "/api/v1/pipeline/manage-data",
            data="invalid json",  # Not JSON
        )

        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    @patch("src.routes.brainstormer_route.get_pipeline_manager")
    async def test_manage_data_all_pipeline_steps(
        self, mock_get_pipeline_manager, async_test_client
    ):
        """Test manage data works for all valid pipeline steps."""
        # Setup mock
        mock_pipeline_manager = AsyncMock()
        mock_get_pipeline_manager.return_value = mock_pipeline_manager

        valid_steps = [
            "market_research",
            "lbc",
            "persona",
            "swot",
            "features",
            "roadmap",
        ]

        for step in valid_steps:
            mock_pipeline_manager.manage_step_data.return_value = {
                "success": True,
                "message": f"Successfully updated data for step {step}",
                "data": {"test": "data"},
            }

            request_data = {
                "run_id": f"test-run-id-{step}",
                "step": step,
                "operation_type": "update",
                "payload": {"test": "data"},
            }

            response = await async_test_client.post(
                "/api/v1/pipeline/manage-data", json=request_data
            )

            assert response.status_code == status.HTTP_200_OK
            response_data = response.json()
            assert response_data["success"] is True
            assert step in response_data["message"]

        # Verify all steps were called
        assert mock_pipeline_manager.manage_step_data.call_count == len(valid_steps)
