#!/usr/bin/env python3
"""
Test runner script for the /pipeline/manage-data endpoint tests.
"""

import os
import sys
import subprocess
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def run_unit_tests():
    """Run the unit tests for the manage data endpoint."""
    print("🧪 Running Unit Tests for /pipeline/manage-data endpoint")
    print("=" * 60)

    # Change to project root directory
    os.chdir(project_root)

    # Run pytest with specific test file
    cmd = [
        sys.executable,
        "-m",
        "pytest",
        "tests/api/test_manage_data_endpoint.py",
        "-v",  # verbose output
        "--tb=short",  # shorter traceback format
        "--color=yes",  # colored output
    ]

    try:
        result = subprocess.run(cmd, capture_output=True, text=True)

        print("STDOUT:")
        print(result.stdout)

        if result.stderr:
            print("STDERR:")
            print(result.stderr)

        if result.returncode == 0:
            print("✅ All unit tests passed!")
        else:
            print(f"❌ Some unit tests failed (exit code: {result.returncode})")

        return result.returncode == 0

    except Exception as e:
        print(f"❌ Error running tests: {e}")
        return False


def run_integration_tests():
    """Run the integration tests for the manage data endpoint."""
    print("\n🚀 Running Integration Tests for /pipeline/manage-data endpoint")
    print("=" * 60)

    # Change to project root directory
    os.chdir(project_root)

    # Run the integration test script
    cmd = [sys.executable, "tests/test_manage_data_integration.py"]

    try:
        result = subprocess.run(cmd, capture_output=True, text=True)

        print("STDOUT:")
        print(result.stdout)

        if result.stderr:
            print("STDERR:")
            print(result.stderr)

        if result.returncode == 0:
            print("✅ Integration tests completed!")
        else:
            print(f"❌ Integration tests failed (exit code: {result.returncode})")

        return result.returncode == 0

    except Exception as e:
        print(f"❌ Error running integration tests: {e}")
        return False


def check_dependencies():
    """Check if required dependencies are installed."""
    required_packages = ["pytest", "httpx", "fastapi"]
    missing_packages = []

    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)

    if missing_packages:
        print(f"❌ Missing required packages: {', '.join(missing_packages)}")
        print("Please install them using:")
        print(f"pip install {' '.join(missing_packages)}")
        return False

    return True


def main():
    """Main function to run all tests."""
    import argparse

    parser = argparse.ArgumentParser(
        description="Test runner for /pipeline/manage-data endpoint"
    )
    parser.add_argument("--unit-only", action="store_true", help="Run only unit tests")
    parser.add_argument(
        "--integration-only", action="store_true", help="Run only integration tests"
    )
    parser.add_argument(
        "--skip-deps-check", action="store_true", help="Skip dependency check"
    )
    args = parser.parse_args()

    print("🔧 Testing /pipeline/manage-data Endpoint")
    print("=" * 60)

    # Check dependencies
    if not args.skip_deps_check:
        print("📦 Checking dependencies...")
        if not check_dependencies():
            return 1
        print("✅ All dependencies are available")
        print()

    success = True

    # Run unit tests
    if not args.integration_only:
        success &= run_unit_tests()

    # Run integration tests
    if not args.unit_only:
        success &= run_integration_tests()

    print("\n" + "=" * 60)
    if success:
        print("🎉 All tests completed successfully!")
        return 0
    else:
        print("❌ Some tests failed. Check the output above for details.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
