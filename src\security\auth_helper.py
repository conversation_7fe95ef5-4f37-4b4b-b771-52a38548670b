from typing import Any, Dict
from fastapi import HTT<PERSON>Exception, Request

# from services.database.project_service import ProjectService
from src.utils.logger import AppLogger

logger = AppLogger(__name__).get_logger()


async def get_authenticated_user(request: Request) -> Dict[str, Any]:
    """
    Get authenticated user information from request state and database.

    This dependency reads the user details that were set by the JWT authentication middleware,
    then fetches or creates the user details from the database and combines them with JWT data.

    Args:
        request: FastAPI Request object containing user state

    Returns:
        Dict[str, Any]: Complete user information including userId, email, userName, realms, and roles

    Raises:
        HTTPException: If user information is not available or database operations fail
    """
    try:
        # Check if user email is available in request state (set by JWT middleware)
        if not hasattr(request.state, "user_email"):
            raise HTTPException(
                status_code=401,
                detail="User authentication information not found. Please ensure you have a valid access-key header.",
            )

        user_email = request.state.user_email

        if not user_email:
            raise HTTPException(
                status_code=401, detail="Invalid user authentication information"
            )

        # Get user details from JWT (set by middleware)
        user_details = getattr(request.state, "user_details", {})

        # # Import here to avoid circular imports
        from src.services.database.project_service import ProjectService

        # Use the connection manager if available, otherwise fall back to direct pool access
        db_manager = getattr(request.app.state, "db_manager", None)
        project_service = ProjectService(request.app.state.db_pool, db_manager)

        # # Get or create user in the database using the email from JWT
        user_id = await project_service.get_or_create_user(user_email)

        logger.info(
            f"User authenticated successfully: {user_email} -> user_id: {user_id}"
        )

        complete_user_info = {
            "userId": user_id,  # Changed from user_id to userId
            "email": user_email,
            "userName": user_details.get("userName", ""),
            "realms": user_details.get("realms", []),
            "roles": user_details.get("roles", []),
        }

        return complete_user_info

    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(
            f"Error retrieving user information for email {getattr(request.state, 'user_email', 'unknown')}: {str(e)}"
        )
        raise HTTPException(
            status_code=500, detail=f"Error retrieving user information: {str(e)}"
        )
