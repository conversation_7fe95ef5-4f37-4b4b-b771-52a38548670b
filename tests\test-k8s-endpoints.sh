#!/bin/bash

# Test script for Kubernetes deployment with /server/product path prefix
# Usage: ./test-k8s-endpoints.sh [BASE_URL]
# Example: ./test-k8s-endpoints.sh http://localhost:8003
# Example: ./test-k8s-endpoints.sh https://your-domain.com

BASE_URL=${1:-"http://localhost:8003"}
ROOT_PATH="/server/product"

echo "Testing Product Studio API endpoints with path prefix: ${ROOT_PATH}"
echo "Base URL: ${BASE_URL}"
echo "=========================================="

# Test root endpoint
echo "1. Testing root endpoint..."
curl -s -w "Status: %{http_code}\n" "${BASE_URL}${ROOT_PATH}/" || echo "Failed"
echo ""

# Test heartbeat endpoint
echo "2. Testing heartbeat endpoint..."
curl -s -w "Status: %{http_code}\n" "${BASE_URL}${ROOT_PATH}/heartbeat" || echo "Failed"
echo ""

# Test health endpoint
echo "3. Testing health endpoint..."
curl -s -w "Status: %{http_code}\n" "${BASE_URL}${ROOT_PATH}/health" | jq '.' 2>/dev/null || curl -s -w "Status: %{http_code}\n" "${BASE_URL}${ROOT_PATH}/health"
echo ""

# Test API documentation (if available)
echo "4. Testing API docs endpoint..."
curl -s -w "Status: %{http_code}\n" -o /dev/null "${BASE_URL}${ROOT_PATH}/docs" && echo "Docs available at: ${BASE_URL}${ROOT_PATH}/docs"
echo ""

# Test pipeline endpoint (should return 422 for missing body, not 404)
echo "5. Testing pipeline start endpoint (POST without body - should return 422)..."
curl -s -w "Status: %{http_code}\n" -X POST "${BASE_URL}${ROOT_PATH}/api/v1/pipeline/start" -H "Content-Type: application/json" || echo "Failed"
echo ""

echo "=========================================="
echo "Test completed!"
echo ""
echo "Expected results:"
echo "- Root endpoint: Status 200"
echo "- Heartbeat: Status 200 with {\"status\":\"ok\"}"
echo "- Health: Status 200 with detailed health info"
echo "- Docs: Status 200 (if enabled)"
echo "- Pipeline: Status 422 (validation error for missing body)"
echo ""
echo "If you see 404 errors, check that ROOT_PATH is correctly configured."
