"""
User Details Decryptor for JWT Authorization Token
This module handles the decryption of user details from the new JWT format where:
1. The JWT contains a 'userDetails' claim that is Base64-encoded AES-encrypted data
2. The AES key is derived from the first 16 bytes of an RSA private key in DER format
3. AES encryption uses ECB mode with PKCS5/PKCS7 padding
"""
import base64
import json
import jwt
import os
import logging
from Crypto.Cipher import AES
from cryptography.hazmat.primitives import serialization
from typing import Dict, Any, Optional
logger = logging.getLogger(__name__)
class UserDetailsDecryptor:
    """
    Handles decryption of user details from JWT tokens using AES encryption
    with a key derived from an RSA private key.
    """
    
    def __init__(self, private_key_path: str):
        """
        Initialize the decryptor with the RSA private key.
        
        Args:
            private_key_path: Path to the RSA private key PEM file
        """
        try:
            # Load the RSA private key from PEM
            with open(private_key_path, "rb") as key_file:
                private_key = serialization.load_pem_private_key(
                    key_file.read(),
                    password=None
                )
            # Get DER-encoded private key bytes
            der_bytes = private_key.private_bytes(
                encoding=serialization.Encoding.DER,
                format=serialization.PrivateFormat.PKCS8,
                encryption_algorithm=serialization.NoEncryption()
            )
            # First 16 bytes → AES key (AES-128)
            aes_key = der_bytes[:16]
            self.cipher = AES.new(aes_key, AES.MODE_ECB)
            
            logger.info(f"UserDetailsDecryptor initialized successfully with key from {private_key_path}")
            
        except Exception as e:
            logger.error(f"Failed to initialize UserDetailsDecryptor: {str(e)}")
            raise
    def decrypt_base64_payload(self, encrypted_base64: str) -> str:
        """
        Decrypt a Base64-encoded AES-encrypted payload.
        
        Args:
            encrypted_base64: Base64-encoded encrypted data
            
        Returns:
            str: Decrypted UTF-8 string
            
        Raises:
            ValueError: If decryption fails
        """
        try:
            # Decode Base64 to get encrypted bytes
            encrypted_bytes = base64.b64decode(encrypted_base64)
            
            # Decrypt using AES-ECB
            decrypted = self.cipher.decrypt(encrypted_bytes)
            # Remove PKCS5/PKCS7 padding
            # Java's AES/PKCS5Padding == PKCS7 in Python
            pad_len = decrypted[-1]
            if pad_len > 16 or pad_len <= 0:
                raise ValueError("Invalid padding length")
            
            decrypted = decrypted[:-pad_len]
            # Convert to UTF-8 string
            return decrypted.decode("utf-8")
            
        except Exception as e:
            logger.error(f"Failed to decrypt Base64 payload: {str(e)}")
            raise ValueError(f"Decryption failed: {str(e)}")
    def decrypt_user_details_from_jwt(self, jwt_token: str) -> str:
        """
        Extract and decrypt user details from a JWT token.
        
        Args:
            jwt_token: JWT token (with or without 'Bearer ' prefix)
            
        Returns:
            str: Decrypted user details as JSON string
            
        Raises:
            ValueError: If JWT is invalid or userDetails claim is missing
        """
        try:
            # Remove 'Bearer ' prefix if present
            if jwt_token.startswith("Bearer "):
                jwt_token = jwt_token[7:]
            # Decode JWT without signature verification
            # (signature verification should be done separately if needed)
            decoded = jwt.decode(jwt_token, options={"verify_signature": False})
            
            # Extract encrypted userDetails claim
            encrypted_payload = decoded.get("userDetails")
            if not encrypted_payload:
                raise ValueError("userDetails claim not found or empty in JWT")
            
            # Decrypt the userDetails
            return self.decrypt_base64_payload(encrypted_payload)
            
        except jwt.InvalidTokenError as e:
            logger.error(f"Invalid JWT token: {str(e)}")
            raise ValueError(f"Invalid JWT token: {str(e)}")
        except Exception as e:
            logger.error(f"Failed to decrypt user details from JWT: {str(e)}")
            raise
    def decrypt_user_details_as_dict(self, jwt_token: str) -> Dict[str, Any]:
        """
        Extract and decrypt user details from JWT as a dictionary.
        
        Args:
            jwt_token: JWT token (with or without 'Bearer ' prefix)
            
        Returns:
            Dict[str, Any]: Decrypted user details as dictionary
            
        Raises:
            ValueError: If decryption fails or JSON is invalid
        """
        try:
            decrypted_str = self.decrypt_user_details_from_jwt(jwt_token)
            return json.loads(decrypted_str)
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse decrypted user details as JSON: {str(e)}")
            raise ValueError(f"Invalid JSON in decrypted user details: {str(e)}")
    def extract_user_detail(self, jwt_token: str, field_name: str) -> Any:
        """
        Extract a specific field from decrypted user details.
        
        Args:
            jwt_token: JWT token
            field_name: Name of the field to extract
            
        Returns:
            Any: Value of the requested field, or None if not found
        """
        try:
            user_details = self.decrypt_user_details_as_dict(jwt_token)
            return user_details.get(field_name)
        except Exception as e:
            logger.error(f"Failed to extract user detail '{field_name}': {str(e)}")
            return None
    def extract_user_id(self, jwt_token: str) -> Optional[int]:
        """
        Extract user ID from decrypted user details.
        
        Args:
            jwt_token: JWT token
            
        Returns:
            int: User ID, or None if not found or invalid
        """
        try:
            user_id = self.extract_user_detail(jwt_token, "userId")
            if user_id is not None:
                return int(user_id)
            return None
        except (ValueError, TypeError) as e:
            logger.error(f"Failed to extract user ID as integer: {str(e)}")
            return None
    def extract_email(self, jwt_token: str) -> Optional[str]:
        """
        Extract email from decrypted user details.
        
        Args:
            jwt_token: JWT token
            
        Returns:
            str: Email address, or None if not found
        """
        return self.extract_user_detail(jwt_token, "email")
    def get_complete_user_info(self, jwt_token: str) -> Dict[str, Any]:
        """
        Get complete user information from JWT token.
        
        Args:
            jwt_token: JWT token
            
        Returns:
            Dict[str, Any]: Complete user information
            
        Raises:
            ValueError: If decryption or parsing fails
        """
        try:
            # First, decode the JWT to get basic claims
            if jwt_token.startswith("Bearer "):
                clean_token = jwt_token[7:]
            else:
                clean_token = jwt_token
                
            decoded_jwt = jwt.decode(clean_token, options={"verify_signature": False})
            
            # Extract email from JWT claims (fallback if not in userDetails)
            jwt_email = (decoded_jwt.get('email') or
                        decoded_jwt.get('unique_name') or
                        decoded_jwt.get('upn') or
                        decoded_jwt.get('preferred_username'))
            
            # Decrypt user details
            user_details = self.decrypt_user_details_as_dict(jwt_token)
            
            # Combine information, prioritizing decrypted user details
            complete_info = {
                "userId": str(user_details.get("userId", "")),
                "email": user_details.get("email", jwt_email or ""),
                "userName": user_details.get("userName", user_details.get("name", "")),
                "realms": user_details.get("realms", []),
                "roles": user_details.get("roles", [])
            }
            
            # Ensure we have at least an email
            if not complete_info["email"]:
                raise ValueError("No email found in JWT or decrypted user details")
            
            return complete_info
            
        except Exception as e:
            logger.error(f"Failed to get complete user info: {str(e)}")
            raise
# Global instance - will be initialized when the application starts
_decryptor_instance: Optional[UserDetailsDecryptor] = None
def get_decryptor() -> UserDetailsDecryptor:
    """
    Get the global UserDetailsDecryptor instance.
    
    Returns:
        UserDetailsDecryptor: The global decryptor instance
        
    Raises:
        RuntimeError: If decryptor is not initialized
    """
    global _decryptor_instance
    if _decryptor_instance is None:
        # Try to initialize with default path
        private_key_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "private_key.pem")
        if os.path.exists(private_key_path):
            _decryptor_instance = UserDetailsDecryptor(private_key_path)
        else:
            raise RuntimeError("UserDetailsDecryptor not initialized. Call initialize_decryptor() first.")
    
    return _decryptor_instance
def initialize_decryptor(private_key_path: str) -> None:
    """
    Initialize the global UserDetailsDecryptor instance.
    
    Args:
        private_key_path: Path to the RSA private key PEM file
    """
    global _decryptor_instance
    _decryptor_instance = UserDetailsDecryptor(private_key_path)
    logger.info("Global UserDetailsDecryptor initialized successfully")