import asyncio
import asyncpg
from typing import Dict, Any
from contextlib import asynccontextmanager
from src.utils.logger import AppLogger

logger = AppLogger(__name__).get_logger()


class DatabaseConnectionManager:
    """
    Enhanced database connection manager with retry logic, connection monitoring,
    and graceful error handling to prevent connection exhaustion.
    """

    def __init__(self, pool: asyncpg.Pool):
        self.pool = pool
        self._connection_stats = {
            "total_acquisitions": 0,
            "failed_acquisitions": 0,
            "timeout_errors": 0,
            "connection_errors": 0,
        }

        # Validate that pool is not None
        if self.pool is None:
            logger.error("DatabaseConnectionManager initialized with None pool")

    @asynccontextmanager
    async def get_connection(self, timeout: float = 30.0, max_retries: int = 3):
        """
        Get a database connection with retry logic and proper error handling.

        Args:
            timeout: Connection acquisition timeout in seconds
            max_retries: Maximum number of retry attempts

        Yields:
            asyncpg.Connection: Database connection

        Raises:
            asyncpg.PostgresError: If connection cannot be acquired after retries
        """
        connection = None
        retry_count = 0

        while retry_count <= max_retries:
            try:
                # Check if pool is None
                if self.pool is None:
                    logger.error("Cannot acquire connection: database pool is None")
                    raise asyncpg.PostgresError("Database pool is not initialized")

                self._connection_stats["total_acquisitions"] += 1

                # Log pool status before acquisition
                if retry_count > 0:
                    logger.warning(
                        f"Retrying connection acquisition (attempt {retry_count + 1}/{max_retries + 1}). "
                        f"Pool stats: size={self.pool.get_size()}, "
                        f"idle={self.pool.get_idle_size()}, "
                        f"max={self.pool.get_max_size()}"
                    )

                # Acquire connection with timeout
                connection = await asyncio.wait_for(
                    self.pool.acquire(), timeout=timeout
                )

                # Log successful acquisition on retry
                if retry_count > 0:
                    logger.info(
                        f"Successfully acquired connection on retry {retry_count + 1}"
                    )

                yield connection
                return  # Success, exit the retry loop

            except asyncio.TimeoutError:
                self._connection_stats["timeout_errors"] += 1
                retry_count += 1

                logger.error(
                    f"Connection acquisition timeout (attempt {retry_count}/{max_retries + 1}). "
                    f"Pool stats: size={self.pool.get_size()}, "
                    f"idle={self.pool.get_idle_size()}, "
                    f"max={self.pool.get_max_size()}"
                )

                if retry_count > max_retries:
                    self._connection_stats["failed_acquisitions"] += 1
                    raise asyncpg.PostgresError(
                        f"Failed to acquire database connection after {max_retries + 1} attempts. "
                        "Database may be overloaded or connection pool exhausted."
                    )

                # Exponential backoff
                await asyncio.sleep(min(2**retry_count, 10))

            except asyncpg.PostgresError as e:
                self._connection_stats["connection_errors"] += 1
                retry_count += 1

                logger.error(
                    f"Database connection error (attempt {retry_count}/{max_retries + 1}): {str(e)}"
                )

                if retry_count > max_retries:
                    self._connection_stats["failed_acquisitions"] += 1
                    raise

                # Wait before retry
                await asyncio.sleep(min(2**retry_count, 10))

            except Exception as e:
                self._connection_stats["connection_errors"] += 1
                logger.error(f"Unexpected error acquiring connection: {str(e)}")
                self._connection_stats["failed_acquisitions"] += 1
                raise asyncpg.PostgresError(f"Unexpected database error: {str(e)}")

            finally:
                # Always release the connection if acquired
                if connection:
                    try:
                        await self.pool.release(connection)
                    except Exception as e:
                        logger.error(f"Error releasing connection: {str(e)}")

    @asynccontextmanager
    async def get_transaction(self, timeout: float = 30.0, max_retries: int = 3):
        """
        Get a database connection with an active transaction.

        Args:
            timeout: Connection acquisition timeout in seconds
            max_retries: Maximum number of retry attempts

        Yields:
            asyncpg.Connection: Database connection with active transaction
        """
        async with self.get_connection(
            timeout=timeout, max_retries=max_retries
        ) as conn:
            async with conn.transaction():
                yield conn

    def get_connection_stats(self) -> Dict[str, Any]:
        """Get connection usage statistics."""
        if self.pool is None:
            return {
                "pool_stats": {"error": "Database pool is not initialized"},
                "connection_stats": self._connection_stats.copy(),
                "pool_utilization": 0,
            }

        pool_stats = {
            "size": self.pool.get_size(),
            "min_size": self.pool.get_min_size(),
            "max_size": self.pool.get_max_size(),
            "idle_connections": self.pool.get_idle_size(),
        }

        return {
            "pool_stats": pool_stats,
            "connection_stats": self._connection_stats.copy(),
            "pool_utilization": (pool_stats["size"] - pool_stats["idle_connections"])
            / pool_stats["max_size"]
            * 100,
        }

    async def health_check(self) -> Dict[str, Any]:
        """
        Perform a health check on the database connection.

        Returns:
            Dict containing health status and statistics
        """
        try:
            async with self.get_connection(timeout=5.0, max_retries=1) as conn:
                await conn.fetchval("SELECT 1")

            stats = self.get_connection_stats()

            return {
                "status": "healthy",
                "message": "Database connection successful",
                **stats,
            }

        except Exception as e:
            return {
                "status": "unhealthy",
                "message": f"Database health check failed: {str(e)}",
                "connection_stats": self._connection_stats.copy(),
            }

    async def cleanup_idle_connections(self):
        """
        Force cleanup of idle connections to free up pool slots.
        This can be called periodically or when connection exhaustion is detected.
        """
        if self.pool is None:
            logger.warning("Cannot cleanup connections: database pool is None")
            return

        try:
            # This will close idle connections that exceed the minimum pool size
            await self.pool.expire_connections()
            logger.info("Cleaned up idle database connections")
        except Exception as e:
            logger.error(f"Error cleaning up idle connections: {str(e)}")
