#!/usr/bin/env python3
"""
Quick test script to verify access-key authentication is working.
"""

import asyncio
import httpx
from src.config.settings import settings


async def test_auth():
    """Test the authentication middleware."""
    base_url = "http://localhost:8000"

    print("Testing API authentication...")

    # Test 1: Request without access-key header (should fail)
    print("\n1. Testing request without access-key header:")
    async with httpx.AsyncClient() as client:
        try:
            response = await client.post(
                f"{base_url}/server/product/api/v1/pipeline/start",
                json={"user_idea": "Test idea"},
            )
            print(f"   Status: {response.status_code}")
            print(f"   Response: {response.text}")
        except Exception as e:
            print(f"   Error: {e}")

    # Test 2: Request with wrong access-key (should fail)
    print("\n2. Testing request with wrong access-key:")
    async with httpx.AsyncClient() as client:
        try:
            response = await client.post(
                f"{base_url}/server/product/api/v1/pipeline/start",
                json={"user_idea": "Test idea"},
                headers={"access-key": "wrong-key"},
            )
            print(f"   Status: {response.status_code}")
            print(f"   Response: {response.text}")
        except Exception as e:
            print(f"   Error: {e}")

    # Test 3: Request with correct access-key (should succeed)
    print("\n3. Testing request with correct access-key:")
    async with httpx.AsyncClient() as client:
        try:
            response = await client.post(
                f"{base_url}/server/product/api/v1/pipeline/start",
                json={"user_idea": "Test idea"},
                headers={"access-key": settings.access_key},
            )
            print(f"   Status: {response.status_code}")
            print(f"   Response: {response.text[:200]}...")
        except Exception as e:
            print(f"   Error: {e}")

    # Test 4: Public endpoint (should work without auth)
    print("\n4. Testing public endpoint (heartbeat):")
    async with httpx.AsyncClient() as client:
        try:
            response = await client.get(f"{base_url}/server/product/heartbeat")
            print(f"   Status: {response.status_code}")
            print(f"   Response: {response.text}")
        except Exception as e:
            print(f"   Error: {e}")


if __name__ == "__main__":
    asyncio.run(test_auth())
