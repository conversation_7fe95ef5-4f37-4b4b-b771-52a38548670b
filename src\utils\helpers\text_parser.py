def get_content_inside_markdown(data: str, markdown: str, find_last: bool = False):
    start_tag = "```" + markdown
    end_tag = "```"

    if find_last:
        start_index = data.rfind(start_tag)
    else:
        start_index = data.find(start_tag)

    if start_index == -1:
        raise

    if find_last:
        end_index = data.rfind(end_tag, start_index + len(start_tag))
    else:
        end_index = data.find(end_tag, start_index + len(start_tag))
    if end_index == -1:
        raise

    output_code = data[start_index + len(start_tag) : end_index].strip()

    return output_code
