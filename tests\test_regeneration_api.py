#!/usr/bin/env python3
"""
Test script for the regeneration API functionality.
This script demonstrates how to use the new regeneration endpoint.
"""

import json
from typing import Dict, Any

# Sample test data for different data types
SAMPLE_LBC_DATA = {
    "problem": [
        "Users struggle to find reliable local services",
        "Lack of transparency in service provider ratings",
        "Difficulty in comparing service prices",
    ],
    "solution": [
        "Platform connecting users with verified local service providers",
        "Transparent rating and review system",
        "Price comparison tool",
    ],
    "key_partners": ["Local service providers", "Payment processors"],
    "value_proposition": ["Reliable service connections", "Transparent pricing"],
    "customer_segments": ["Homeowners", "Small business owners"],
    "revenue_streams": ["Commission from bookings", "Premium listings"],
    "key_metrics": ["Number of bookings", "Customer satisfaction score"],
    "alternatives": ["Traditional directories", "Word of mouth"],
    "solution_tenants": ["User App", "Provider Dashboard", "Admin Panel"],
}

SAMPLE_PERSONA_DATA = {
    "personas": [
        {
            "name": "Busy Professional",
            "role": "Marketing Manager",
            "age": 32,
            "gender": "Female",
            "education": "Bachelor's Degree",
            "status": "Married",
            "location": "Urban area",
            "techLiteracy": "High",
            "avatar": None,
            "quote": "I need reliable services that fit my busy schedule",
            "personality": ["Time-conscious", "Quality-focused", "Tech-savvy"],
            "painPoints": [
                "Limited time to research service providers",
                "Uncertainty about service quality",
                "Difficulty scheduling around work hours",
            ],
            "goals": ["Find reliable services quickly", "Maintain work-life balance"],
            "motivation": ["Efficiency", "Quality assurance"],
            "expectations": ["Quick booking process", "Reliable service delivery"],
            "skills": [
                {"name": "Digital literacy", "level": 90},
                {"name": "Time management", "level": 85},
            ],
            "devices": ["mobile", "laptop"],
        }
    ]
}


def create_regeneration_request(
    data_type: str,
    field_to_regenerate: str,
    current_data: Dict[str, Any],
    user_request: str,
    additional_context: Dict[str, Any] = None,
) -> Dict[str, Any]:
    """Create a regeneration request payload."""
    return {
        "run_id": "test-run-123",
        "data_type": data_type,
        "field_to_regenerate": field_to_regenerate,
        "current_data": current_data,
        "user_request": user_request,
        "additional_context": additional_context or {},
    }


def test_lbc_problems_regeneration():
    """Test regenerating problems in Lean Business Canvas."""
    request = create_regeneration_request(
        data_type="lbc",
        field_to_regenerate="problem",
        current_data=SAMPLE_LBC_DATA,
        user_request="Make the problems more specific to small business owners who need accounting services",
        additional_context={
            "target_audience": "small business owners",
            "service_type": "accounting",
        },
    )

    print("=== LBC Problems Regeneration Test ===")
    print("Request payload:")
    print(json.dumps(request, indent=2))
    print("\n")


def test_persona_painpoints_regeneration():
    """Test regenerating pain points in user persona."""
    request = create_regeneration_request(
        data_type="persona",
        field_to_regenerate="painPoints",
        current_data=SAMPLE_PERSONA_DATA,
        user_request="Focus the pain points on healthcare service booking challenges",
        additional_context={
            "industry": "healthcare",
            "service_focus": "appointment booking",
        },
    )

    print("=== Persona Pain Points Regeneration Test ===")
    print("Request payload:")
    print(json.dumps(request, indent=2))
    print("\n")


def test_multiple_fields_regeneration():
    """Test regenerating multiple related fields."""
    request = create_regeneration_request(
        data_type="lbc",
        field_to_regenerate="problem,solution",
        current_data=SAMPLE_LBC_DATA,
        user_request="Pivot the business model to focus on eco-friendly home services",
        additional_context={
            "focus": "sustainability",
            "market": "eco-conscious consumers",
        },
    )

    print("=== Multiple Fields Regeneration Test ===")
    print("Request payload:")
    print(json.dumps(request, indent=2))
    print("\n")


if __name__ == "__main__":
    print("Regeneration API Test Cases")
    print("=" * 50)

    test_lbc_problems_regeneration()
    test_persona_painpoints_regeneration()
    test_multiple_fields_regeneration()

    print("Test cases generated successfully!")
    print("\nTo test the API, send POST requests to:")
    print("http://localhost:8000/brainstormer/regenerate")
    print("\nWith the above payloads and appropriate authentication headers.")
