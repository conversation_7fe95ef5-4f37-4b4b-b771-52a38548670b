import uuid
from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException, Header, Request
from pydantic import BaseModel
from src.security.auth_helper import get_authenticated_user
from src.services.aava_service import ConversationalAgentService, PipelineAgentService
from src.pipeline_manager import PipelineManager
from src.state_manager import RedisManager
from src.config.settings import settings
from src.utils.logger import AppLogger
from typing import Dict, Any, List
from src.models.brainstormer_models import RegenerateRequest, RegenerateResponse

# Create router instead of app
brainstormer_router = APIRouter(tags=["brainstormer"])
logger = AppLogger("Api_Client_Logger").get_logger()


# --- API Request Models ---
class StartRequest(BaseModel):
    user_idea: str
    project_name: str
    user_groups: List[str]
    industry: str


class ChatRequest(BaseModel):
    run_id: str
    current_step: str
    message: str


class NextStepRequest(BaseModel):
    run_id: str
    current_step: str


class PromptRequest(BaseModel):
    prompt: str


class PipelineDataManagementRequest(BaseModel):
    run_id: str
    step: str
    operation_type: str  # "update", "delete", "add"
    payload: Dict[str, Any]


class ProjectDetailsRequest(BaseModel):
    run_id: str


# --- Dependency Injection Setup ---
# Services will be initialized after configuration is loaded
_redis_manager = None
_pipeline_agent = None
_convo_agent = None
_project_service = None


def get_services():
    """Initialize services if not already done"""
    global _redis_manager, _pipeline_agent, _convo_agent, _project_service

    if _redis_manager is None:
        _redis_manager = RedisManager(url=settings.redis_url)
    if _pipeline_agent is None:
        _pipeline_agent = PipelineAgentService(base_url=settings.base_url)
    if _convo_agent is None:
        _convo_agent = ConversationalAgentService(base_url=settings.base_url)

    return _redis_manager, _pipeline_agent, _convo_agent


def get_pipeline_manager(request: Request):
    db_pool = request.app.state.db_pool
    db_manager = request.app.state.db_manager
    redis_manager, pipeline_agent, convo_agent = get_services()
    return PipelineManager(
        redis_manager, pipeline_agent, convo_agent, db_pool, db_manager
    )


@brainstormer_router.post("/pipeline/start", status_code=201)
async def start_pipeline(
    request: StartRequest,
    req: Request,
    user: dict = Depends(get_authenticated_user),
    authorization: str = Header(
        ..., alias="Authorization", description="JWT token for authentication"
    ),
):
    """Starts a new pipeline run and returns the result of the first step."""
    run_id = str(uuid.uuid4())
    try:
        pipeline_manager = get_pipeline_manager(req)

        # Create project in database
        from src.services.database.project_service import ProjectService

        project_service = ProjectService(
            req.app.state.db_pool, req.app.state.db_manager
        )

        await project_service.create_project(
            name=request.project_name,
            description=f"Project for idea: {request.user_idea}",
            created_by=user["userId"],  # Assuming user dict has user_id
            run_id=uuid.UUID(run_id),
        )

        first_step_data = await pipeline_manager.start_new_run(
            run_id, request.user_idea, user["email"]
        )
        return {"run_id": run_id, "step": "market_research", "data": first_step_data}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@brainstormer_router.post("/pipeline/next", status_code=200)
async def next_pipeline_step(
    request: NextStepRequest,
    req: Request,
    user: dict = Depends(get_authenticated_user),
    authorization: str = Header(
        ..., alias="Authorization", description="JWT token for authentication"
    ),
):
    """Triggers the next step of the pipeline."""
    try:
        next_step_data = await get_pipeline_manager(req).trigger_next_step(
            request.run_id, request.current_step, user["email"]
        )
        return next_step_data
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@brainstormer_router.post("/pipeline/chat")
async def chat_handler(
    request: ChatRequest,
    req: Request,
    user: dict = Depends(get_authenticated_user),
    authorization: str = Header(
        ..., alias="Authorization", description="JWT token for authentication"
    ),
):
    """Handles a user's chat message for editing or delegation."""
    try:
        response = await get_pipeline_manager(req).handle_chat_message(
            run_id=request.run_id,
            current_step=request.current_step,
            user_message=request.message,
            user_signature=user["email"],
        )
        return response
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@brainstormer_router.post("/identify-details")
async def identify_details(
    request: PromptRequest,
    req: Request,
    user: dict = Depends(get_authenticated_user),
    authorization: str = Header(
        ..., alias="Authorization", description="JWT token for authentication"
    ),
):
    """Takes a user prompt and identifies details by making an API call."""
    try:
        redis_manager, pipeline_agent, convo_agent = get_services()

        # Use the conversational agent to process the prompt
        response = await pipeline_agent.process_user_prompt(
            prompt=request.prompt, user_signature=user["email"]
        )

        return response
    except Exception as e:
        logger.error(f"Error in identify_details: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@brainstormer_router.post("/pipeline/manage-data")
async def manage_pipeline_data(
    request: PipelineDataManagementRequest,
    req: Request,
    user: dict = Depends(get_authenticated_user),
    authorization: str = Header(
        ..., alias="Authorization", description="JWT token for authentication"
    ),
):
    """Manage pipeline data - update, delete, or add data to pipeline steps."""
    try:
        pipeline_manager = get_pipeline_manager(req)

        result = await pipeline_manager.manage_step_data(
            run_id=request.run_id,
            step=request.step,
            operation_type=request.operation_type,
            payload=request.payload,
            user_signature=user["email"],
        )

        return result
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error in manage_pipeline_data: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@brainstormer_router.post("/enhance")
async def enhance_prompt(
    request: PromptRequest,
    req: Request,
    user: dict = Depends(get_authenticated_user),
    authorization: str = Header(
        ..., alias="Authorization", description="JWT token for authentication"
    ),
):
    """Manage pipeline data - update, delete, or add data to pipeline steps."""
    try:
        _, pipeline_agent, _ = get_services()

        result = await pipeline_agent.enhance_prompt(
            prompt=request.prompt,
            user_signature=user["email"],
        )

        return result
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error in manage_pipeline_data: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@brainstormer_router.post("/project/details")
async def get_project_details(
    request: ProjectDetailsRequest,
    req: Request,
    user: dict = Depends(get_authenticated_user),
    authorization: str = Header(
        ..., alias="Authorization", description="JWT token for authentication"
    ),
):
    """Get comprehensive project details including all related data."""
    try:
        from src.services.database.project_service import ProjectService
        from uuid import UUID

        project_service = ProjectService(
            req.app.state.db_pool, req.app.state.db_manager
        )

        # Convert string run_id to UUID
        try:
            run_id_uuid = UUID(request.run_id)
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid run_id format")

        # Get comprehensive project details
        project_details = await project_service.get_all_project_details_by_run_id(
            run_id_uuid
        )

        if not project_details:
            raise HTTPException(status_code=404, detail="Project not found")

        # Check if user has access to this project (optional security check)
        if project_details["project"]["created_by"] != user["userId"]:
            raise HTTPException(status_code=403, detail="Access denied to this project")

        return project_details

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in get_project_details: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@brainstormer_router.get("/projects/list")
async def get_user_projects(
    req: Request,
    user: dict = Depends(get_authenticated_user),
    authorization: str = Header(
        ..., alias="Authorization", description="JWT token for authentication"
    ),
):
    """Get all projects created by the authenticated user."""
    try:
        from src.services.database.project_service import ProjectService

        project_service = ProjectService(
            req.app.state.db_pool, req.app.state.db_manager
        )

        # Get all projects for the user
        projects = await project_service.get_all_projects_for_user(user["userId"])

        return {"projects": projects, "total_count": len(projects)}

    except Exception as e:
        logger.error(f"Error in get_user_projects: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@brainstormer_router.post("/regenerate", response_model=RegenerateResponse)
async def regenerate_data_portion(
    request: RegenerateRequest,
    req: Request,
    user: dict = Depends(get_authenticated_user),
    authorization: str = Header(
        ..., alias="Authorization", description="JWT token for authentication"
    ),
):
    """Regenerate a specific portion of data while maintaining the overall structure."""
    try:
        # _, pipeline_agent, _, project_service = get_services()
        pipeline_manager = get_pipeline_manager(req)

        # pipeline_manager.pipeline_agent.set_project_service(project_service)

        # Call the regeneration service
        regenerated_data = (
            await pipeline_manager.pipeline_agent.regenerate_data_portion(
                run_id=request.run_id,
                current_data=request.additional_context,
                user_request=request.user_request,
                user_signature=user["email"],
                step_name=request.step,
            )
        )

        return RegenerateResponse(
            success=True,
            message=f"Successfully regenerated for {request.step}",
            updated_fields=regenerated_data,  # Make sure regenerated_data contains the updated fields
        )

    except ValueError as e:
        logger.error(f"Validation error in regenerate_data_portion: {str(e)}")
        return RegenerateResponse(
            success=False,
            message=f"Validation error: {str(e)}",
            regenerated_data=None,
        )
    except Exception as e:
        logger.error(f"Error in regenerate_data_portion: {str(e)}")
        return RegenerateResponse(
            success=False,
            message=f"Internal server error: {str(e)}",
            regenerated_data=None,
        )
