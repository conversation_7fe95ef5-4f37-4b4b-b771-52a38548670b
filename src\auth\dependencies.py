"""
Authentication dependencies for FastAPI routes.
"""

from fastapi import <PERSON><PERSON><PERSON><PERSON>xception, Header
from src.config.settings import settings


async def verify_access_key(access_key: str = Head<PERSON>(None, alias="access-key")):
    """
    Dependency to verify access-key header.

    Args:
        access_key: The access-key from request headers

    Returns:
        str: The validated access key

    Raises:
        HTTPException: If access key is missing or invalid
    """
    if not access_key:
        raise HTTPException(status_code=401, detail="Missing access-key header")

    if access_key != settings.access_key:
        raise HTTPException(status_code=403, detail="Invalid access-key")

    return access_key
