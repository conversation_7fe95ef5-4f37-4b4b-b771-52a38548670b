AAVA Product Studio API Service - Python to Java Migration Blueprint (Directives Only)
1. Root Level Files & Directives:
main.py
Purpose: Entry point. Imports src.main:app and runs Uvicorn server on port 8000.
Usage: python main.py starts the application.
Java Translation: Replace with Spring Boot main class:
pyproject.toml
Purpose: Defines 29 production dependencies including FastAPI, AsyncPG, Redis, PyJWT.
Usage: UV package manager reads this for dependency resolution.
Java Translation: Replace with Maven pom.xml:
fastapi[standard]>=0.115.11 → spring-boot-starter-webflux
asyncpg==0.30.0 → spring-boot-starter-data-r2dbc + r2dbc-postgresql
redis[hiredis]>=6.2.0 → spring-boot-starter-data-redis-reactive
pyjwt==2.10.1 → spring-security-oauth2-jose
cryptography>=45.0.6 → Built-in JCA
pydantic-settings>=2.10.1 → spring-boot-configuration-processor
 uv.lock
Purpose: Locked dependency versions for reproducible builds.
Usage: Ensures exact dependency versions across environments.
Java Translation: Maven/Gradle dependency resolution handles this automatically. No direct equivalent needed.
 .env
Purpose: Contains APP_CONFIG_API_URL=https://aava-dev.avateam.io/appconfig.
Usage: Local development configuration.
Java Translation: Move to application-dev.yml:
 .env.docker
Purpose: 29 lines of Docker environment variables including DB credentials, Redis URL, API endpoints.
Usage: Docker Compose environment configuration.
Java Translation: Update Docker Compose with Java-specific environment variables. Maintain same variable names for consistency.
Dockerfile
Purpose: Python 3.13.7-slim base, UV package manager, non-root user, port 8000.
Usage: Builds containerized Python application.
Java Translation: Replace with multi-stage Java Dockerfile:
Loading...
docker-compose.yml
Purpose: Defines app + Redis services with environment variables and networking.
Usage: Local development orchestration.
Java Translation: Update app service to use new Java Docker image. Maintain Redis service unchanged.
azure-pipelines.yml
Purpose: CI/CD with Docker build, ACR push, Helm deployment. Triggers every 6 hours.
Usage: Automated deployment pipeline.
Java Translation: Replace Python build steps with Maven compilation, JAR creation, and Docker build for Java application.
private_key.pem
Purpose: RSA private key for AES key derivation in JWT user details decryption.
Usage: First 16 bytes of DER-encoded key used as AES-128 key.
Java Translation: Convert to Java KeyStore or manage as environment variable. Implement identical AES-ECB decryption logic in UserDetailsDecryptor component.
pytest.ini
Purpose: Pytest configuration with async support, markers, test paths.
Usage: Test framework configuration.
Java Translation: Replace with Maven Surefire/Failsafe configuration and junit-platform.properties.
.pre-commit-config.yaml
Purpose: Git hooks for Ruff linting, Gitleaks security scanning, pip-audit.
Usage: Code quality enforcement.
Java Translation: Implement equivalent with Husky + Google Java Format + SpotBugs + OWASP Dependency Check.
2. src/ Directory - Core Application Components:
 src/main.py
Purpose: FastAPI application factory. Configures CORS, JWT middleware, database pool, health endpoints.
Key Functions:
lifespan(): Async context manager for startup/shutdown
health_check(): Database + Redis connectivity check
database_statistics(): Connection pool metrics
Usage: Creates FastAPI app with middleware stack and routes.
Java Translation:
Replace with @Configuration classes for CORS, security, database
lifespan() → @EventListener(ApplicationReadyEvent.class) and @PreDestroy
Health endpoints → Spring Boot Actuator with custom health indicators
Database pool → HikariCP auto-configuration
 src/dependencies.py
Purpose: Empty file (0 lines).
Usage: Placeholder for shared dependencies.
Java Translation: Not applicable.
 src/pipeline_manager.py
Purpose: Orchestrates multi-step AI pipeline execution with Redis state management.
Key Classes:
PipelineManager: Main orchestrator with Redis, database, and AI services
Key Functions:
_gather_context_for_step(): Loads previous step data from Redis/DB
start_pipeline(): Initializes new pipeline execution
chat_with_pipeline(): Handles conversational interactions
next_step(): Progresses pipeline to next stage
Usage: Called by routes for pipeline operations.
Java Translation:
Implement as @Service class
Use @Autowired for Redis, database, AI services
Replace async methods with Mono<T> return types
Context gathering → reactive Redis/database queries
 src/state_manager.py
Purpose: Redis client wrapper for pipeline state persistence.
Key Classes:
RedisManager: Async Redis operations with JSON serialization
Key Functions:
save_step_data(): Stores pipeline step data as JSON
load_step_data(): Retrieves and deserializes step data
Usage: Used by PipelineManager for state persistence.
Java Translation:
Implement as @Service with ReactiveRedisTemplate
Use Jackson for JSON serialization
Replace async methods with Mono<T> return types
2.1.  src/auth/:
 src/auth/dependencies.py
Purpose: FastAPI dependency for authenticated user extraction.
Key Functions:
Dependency injection for route authentication
Usage: Used in route handlers via Depends().
Java Translation: Replace with Spring Security @PreAuthorize annotations and Authentication parameter injection.
2.2.  src/config/:
 src/config/settings.py
Purpose: Pydantic settings class with runtime configuration fetching.
Key Classes:
Settings: Pydantic BaseSettings with database, API endpoints, Redis config
Key Functions:
fetch_runtime_config(): Async API call to external config service
Usage: Global settings instance used throughout application.
Java Translation:
Create @ConfigurationProperties class
Implement @EventListener(ApplicationReadyEvent.class) for runtime config fetching
Use WebClient for external config API calls
 src/config/brainstormer_prompt.py
Purpose: 522 lines of AI prompt templates for different pipeline steps.
Key Constants:
MARKET_RESEARCH_PROMPT: Market analysis prompt template
LBC_PROMPT: Lean Business Canvas prompt
PERSONA_PROMPT: User persona generation prompt
SWOT_PROMPT: SWOT analysis prompt
FEATURE_PROMPT: Feature prioritization prompt
ROADMAP_PROMPT: Roadmap generation prompt
Usage: Used by AI services for prompt formatting.
Java Translation:
Externalize to application.yml or separate properties files
Create @Component class for prompt management
Use Spring's @Value for prompt injection
 src/config/error_codes.py
Purpose: Enum defining API error codes.
Key Classes:
APIErrorCode: String enum with error constants
Usage: Used in custom exception classes.
Java Translation:
Create Java enum APIErrorCode
Implement @ControllerAdvice for global exception handling
 src/config/pipeline_config.py
Purpose: Pipeline step definitions and agent configurations.
Key Constants:
PIPELINE_STEPS: List of pipeline step names
PIPELINE_CONTEXT_DEFINITION: Step dependencies mapping
AGENT_NAMES: Agent name and prompt mapping
Usage: Used by PipelineManager for step orchestration.
Java Translation:
Create @Configuration class with @Bean methods
Define constants as static final fields
Use Map<String, AgentConfiguration> for agent configs
2.3.  src/database/:
 src/database/schema.sql
Purpose: PostgreSQL schema with JSONB columns for pipeline data.
Key Tables:
aava_prod_studio.projects: Main project table with UUID run_id
market_research: Step 0 data with competitors
lean_business_canvas: Step 1 data with JSONB arrays
user_personas: Step 2 data with JSONB personality traits
swot_analysis: Step 3 data with JSONB SWOT arrays
features: Step 4 data with MoSCoW prioritization
roadmap_tasks: Step 5 data with quarterly planning
project_conversations: Chat history with message ordering
Usage: Database schema definition for pipeline data storage.
Java Translation:
Use Flyway for schema migration
Create JPA @Entity classes for each table
Use @JdbcTypeCode(SqlTypes.JSON) for JSONB columns
Implement R2DBC repositories for reactive database access
2.4.  src/models/:
 src/models/aava_models.py
Purpose: Core API response models and exception classes.
Key Classes:
Choice: AI response choice with text, index, identifier
Response: AI response with choices array
DAResponse: Wrapper for AI responses
BaseAPIException: Custom HTTP exception with error codes
DAValueError: Business logic validation errors (400)
DAServerError: Server/platform errors (500)
Usage: API response serialization and error handling.
Java Translation:
Create DTO classes with Jackson annotations
Implement custom exception classes extending RuntimeException
Use @ResponseStatus annotations for HTTP status mapping
 src/models/brainstormer_models.py
Purpose: 177 lines of Pydantic models for pipeline steps.
Key Classes:
Competitor: Name, URL, strengths
MarketResearch: Market summary, competitors list, gaps
LeanBusinessCanvas: 9 JSONB arrays for business canvas elements
UserPersona: Demographics, personality traits, pain points (6 JSONB arrays)
SwotAnalysis: 4 JSONB arrays for SWOT elements
Feature: MoSCoW prioritized features with tags
FeaturesByCategory: Features grouped by priority
RoadmapTask: Quarterly tasks with priority and duration
RegenerateRequest/Response: API models for data regeneration
Usage: Request/response validation and database entity mapping.
Java Translation:
Create DTO classes with Bean Validation annotations
Use @NotNull, @Size, @Min, @Max, @Pattern for validation
Create separate JPA entities for database mapping
Use List<String> for JSONB arrays with custom converters
 src/models/common.py
Purpose: Shared data structures.
Key Classes:
ErrorDetail: Field-level error information
Usage: Common error response structure.
Java Translation: Create ErrorDetail DTO class for validation error responses.
2.5. src/routes/:
 src/routes/brainstormer_route.py
Purpose: 361 lines defining 7 API endpoints for pipeline operations.
Key Classes:
StartRequest: Project initialization parameters
ChatRequest: Conversational interaction parameters
NextStepRequest: Pipeline progression parameters
PipelineDataManagementRequest: CRUD operation parameters
Key Endpoints:
POST /start: Initialize new pipeline with user idea
POST /chat: Interactive AI conversation
POST /next-step: Progress to next pipeline stage
POST /regenerate: Regenerate specific step data
GET /project-details/{run_id}: Retrieve complete project data
POST /manage-data: CRUD operations on pipeline data
GET /projects/list: List user's projects
Usage: HTTP request handling with JWT authentication.
Java Translation:
Create @RestController class with @RequestMapping("/server/product/api/v1")
Map each endpoint to method with appropriate @PostMapping/@GetMapping
Use @Valid @RequestBody for request validation
Inject services via @Autowired
Return Mono<ResponseEntity<T>> for reactive responses
2.6.  src/security/:
 src/security/auth_helper.py
Purpose: Authentication dependency for route handlers.
Key Functions:
get_authenticated_user(): Extracts user from JWT and creates/retrieves from database
Usage: FastAPI dependency injection for authenticated routes.
Java Translation:
Create @Component class for user extraction
Use Spring Security Authentication parameter injection
Implement user creation/retrieval logic with reactive repositories
 src/security/jwt_auth_middleware.py
Purpose: 201 lines of FastAPI middleware for JWT validation.
Key Classes:
JWTAuthMiddleware: Intercepts requests, validates JWT, sets user context
Key Functions:
dispatch(): Main middleware logic with public route exemption
_is_public_route(): Checks if route requires authentication
Usage: Applied globally to FastAPI application.
Java Translation:
Implement as Spring Security filter extending OncePerRequestFilter
Configure in SecurityWebFilterChain with path matchers
Use reactive JWT decoder for token validation
 src/security/jwt_decoder.py
Purpose: 141 lines of JWT token extraction and basic parsing.
Key Functions:
decode_jwt_and_extract_user_details(): Extracts JWT from headers, attempts decryption
decode_base64url(): Base64URL decoding utility
Usage: Called by JWT middleware for token processing.
Java Translation:
Create ReactiveJwtDecoder implementation
Use JJWT library for token parsing
Implement fallback logic for decryption failures
 src/security/user_details_decryptor.py
Purpose: 264 lines of complex AES decryption for JWT user details.
Key Classes:
UserDetailsDecryptor: AES-ECB decryption using RSA key derivation
Key Functions:
__init__(): Loads RSA key, derives AES key from first 16 bytes of DER encoding
decrypt_base64_payload(): AES-ECB decryption with PKCS7 padding removal
get_complete_user_info(): Extracts and decrypts userDetails claim from JWT
Usage: Critical for user authentication and authorization.
Java Translation:
Create @Component class with identical AES-ECB logic
Use Java Cryptography Architecture (JCA)
Load RSA key from KeyStore or environment variable
CRITICAL: Exact replication of padding removal and key derivation logic required
2.7.  src/services/:
 src/services/aava_service.py
Purpose: 996 lines of core business logic for AI service integration.
Key Classes:
AAVAAIService: Base class for AI service communication
ConversationalAgentService: Chat interactions with AI
PipelineAgentService: Structured pipeline execution with polling
Key Functions:
make_api_call(): HTTP POST to AI services
chat_with_agent(): Conversational AI interaction
generate_artifact(): Full pipeline step generation
perform_web_search(): Web search with async polling
regenerate_data_portion(): Selective data regeneration
Usage: Called by routes and pipeline manager for AI operations.
Java Translation:
Create @Service classes for each AI service type
Use WebClient for reactive HTTP calls
Implement polling with Mono.delayElement() and retry logic
Use @Retryable for transient failures
Challenge: Complex async polling and error handling logic
src/services/database/project_service.py
Purpose: 823 lines of database operations for project management.
Key Classes:
ProjectService: CRUD operations with JSONB parsing
Key Functions:
_parse_jsonb_field(): Converts JSONB strings to Python objects
create_project(): Project creation with user association
get_all_project_details(): Comprehensive project data retrieval
get_or_create_user(): User management with RBAC integration
Multiple CRUD methods for each pipeline step
Usage: Database abstraction layer for all project operations.
Java Translation:
Create @Service class with R2DBC repositories
Use @JdbcTypeCode(SqlTypes.JSON) for automatic JSONB handling
Implement reactive CRUD operations returning Mono<T>/Flux<T>
Create custom repository methods for complex queries
src/services/database/user_service.py
Purpose: 166 lines of user management operations.
Key Classes:
UserService: User CRUD operations
Key Functions:
create_user(): User creation
get_user_by_email(): User lookup
update_user(): Dynamic user updates
Usage: User management for authentication system.
Java Translation:
Create @Service class with user repository
Implement reactive user operations
Use Spring Data JPA query methods
2.8.  src/utils/:
 src/utils/db_connection_manager.py
Purpose: 212 lines of database connection management with retry logic.
Key Classes:
DatabaseConnectionManager: Connection lifecycle management
Key Functions:
get_connection(): Connection acquisition with retry
get_transaction(): Transaction management
health_check(): Database connectivity verification
cleanup_idle_connections(): Connection pool maintenance
Usage: Database connection abstraction for services.
Java Translation:
Replace with Spring Boot auto-configured DataSource
Use HikariCP connection pool configuration
Implement custom health indicators for monitoring
Transaction management handled by @Transactional
 src/utils/logger.py
Purpose: 74 lines of custom logging configuration.
Key Classes:
AppLogger: Singleton logger with color formatting
CustomFormatter: Color-coded console output
Usage: Application-wide logging interface.
Java Translation:
Replace with SLF4J + Logback configuration
Configure colored console appender in logback-spring.xml
Use @Slf4j annotation for logger injection
src/utils/fragment_processor.py
Purpose: 136 lines of data transformation for AI agents.
Key Classes:
FragmentProcessor: Transforms request models for different AI use cases
Key Functions:
get_input_prompt(): Generates prompts based on use case
prepare_input(): Formats input with schema validation
get_output_fragment(): Validates AI responses
Usage: Data preparation for AI service calls.
Java Translation:
Create @Service class for data transformation
Use Jackson for JSON schema generation
Implement validation with Bean Validation framework
 src/utils/api_helper/api_client.py
Purpose: 119 lines of HTTP client wrapper with error handling.
Key Classes:
APIClient: Async HTTP client with retry logic
Key Functions:
get(): HTTP GET with error handling
post(): HTTP POST with JSON/form data
_handle_response(): Response processing and error mapping
Usage: External API communication.
Java Translation:
Create @Component class using WebClient
Implement reactive error handling with onErrorMap()
Use Resilience4j for retry and circuit breaker patterns
 src/utils/api_helper/retry_config.py
Purpose: Retry configuration constants.
Usage: Defines retry parameters for API calls.
Java Translation: Configure in application.yml and use with @Retryable annotations.
 src/utils/decorators/avaplus_decorator.py
Purpose: 21 lines of exception handling decorator.
Key Functions:
handle_ava_exceptions(): Wraps functions with error handling
Usage: Applied to service methods for consistent error handling.
Java Translation:
Implement as Spring AOP @Aspect
Use @Around advice for exception handling
Create custom annotation for method marking
 src/utils/helpers/text_parser.py
Purpose: 23 lines of markdown content extraction.
Key Functions:
get_content_inside_markdown(): Extracts content between markdown code blocks
Usage: Parsing AI responses for structured data.
Java Translation: Create utility class with static methods for text processing.
 src/utils/debug_file_logger.py
Purpose: Debug-specific file logging.
Usage: Detailed logging for debugging purposes.
Java Translation: Configure separate file appender in Logback for debug logging.
MIGRATION EXECUTION PRIORITY:

Database schema and JPA entities (Foundation)
Security components (JWT decoder, user decryptor) (Critical path)
Core services (AI integration, project management) (Business logic)
API controllers (External interface)
Utilities and configuration (Supporting infrastructure)
CRITICAL DEPENDENCIES:

User details decryption logic must be exactly replicated
JSONB handling requires Hibernate 6+ or custom converters
Async operations require reactive programming throughout
External AI service integration patterns must maintain polling behavior
0 files changed