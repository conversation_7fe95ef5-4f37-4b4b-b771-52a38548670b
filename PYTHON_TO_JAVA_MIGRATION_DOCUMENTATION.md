# AAVA Product Studio API Service - Python to Java Migration Documentation

## 1. Project Overview & High-Level Architecture

### Project Name
**AAVA Product Studio API Service** (`product-studio-server`)

### Primary Purpose
This is a FastAPI-based microservice that orchestrates AI-powered product development workflows. The service manages a multi-step pipeline for product ideation, including market research, business canvas creation, user persona development, SWOT analysis, feature prioritization, and roadmap generation.

### Architectural Style
- **RESTful API Microservice** with asynchronous processing capabilities
- **Event-driven architecture** using Redis for state management
- **Pipeline orchestration** pattern for multi-step AI workflows
- **Database-first design** with PostgreSQL for persistence

### Key Technologies/Frameworks (Python)
- **Web Framework**: FastAPI 0.115.11 with Uvicorn ASGI server
- **Database**: PostgreSQL with AsyncPG driver (no ORM)
- **Caching/State**: Redis with async client
- **Authentication**: JWT-based with custom middleware
- **HTTP Client**: HTTPX for external API calls
- **Data Validation**: Pydantic models
- **Async Framework**: Native Python asyncio
- **Testing**: Pytest with async support
- **Package Management**: UV (modern Python package manager)

### External Integrations
- **AI Agent Services**: Pipeline Agent Service and Individual Agent Service for AI processing
- **Configuration Service**: External config API for runtime configuration
- **Authentication Provider**: JWT token validation with user details decryption
- **RBAC System**: References `rbac.users` table for user management

### Deployment Strategy
- **Containerized**: Docker with Python 3.13.7-slim base image
- **Container Orchestration**: Kubernetes (inferred from Helm charts in Azure Pipeline)
- **CI/CD**: Azure DevOps with automated builds every 6 hours
- **Service Mesh**: Configured for `/server/product` path prefix
- **Health Monitoring**: Built-in health checks and database statistics endpoints

## 2. Core Components & Their Responsibilities

### `main.py` (root) and `src/main.py`
**Root `main.py`**: Simple entry point that imports and runs the FastAPI app
**`src/main.py`**: Primary application initialization containing:
- **FastAPI Application Setup**: Custom docs URLs, CORS middleware configuration
- **Database Connection Management**: AsyncPG connection pool with optimized settings
- **Lifecycle Management**: Startup/shutdown hooks for resource management
- **Middleware Stack**: CORS + JWT Authentication middleware
- **Health Endpoints**: `/heartbeat`, `/health`, `/db-stats`, `/db-cleanup`
- **Global State**: Database pool and connection manager attached to app state

**Java Mapping Consideration**: Translates to Spring Boot main class with `@SpringBootApplication`, connection pool configuration in `application.yml`, and health actuator endpoints.

### `src/routes/brainstormer_route.py`
**Purpose**: Defines all API endpoints for the product development pipeline
**Key Endpoints**:
- `POST /start` - Initialize new product development session
- `POST /chat` - Interactive conversation with AI agents
- `POST /next-step` - Progress to next pipeline step
- `POST /regenerate` - Regenerate specific pipeline step data
- `GET /project-details/{run_id}` - Retrieve complete project information
- `POST /manage-data` - CRUD operations on pipeline data

**Request/Response Structures**: Pydantic models for validation
**Authentication**: All endpoints require JWT token via `access-key` header

**Java Mapping Consideration**: Translates to Spring `@RestController` with `@RequestMapping`, `@PostMapping`, `@GetMapping` annotations. Request/response DTOs replace Pydantic models.

### `src/services/aava_service.py`
**Purpose**: Business logic layer for AI agent interactions
**Key Services**:
- **`ConversationalAgentService`**: Handles chat interactions with AI
- **`PipelineAgentService`**: Manages structured pipeline execution
- **`AAVAAIService`**: Base class for AI service communication

**Dependencies**: External AI services, database project service, Redis state management
**Core Functions**: API payload construction, response parsing, data transformation

**Java Mapping Consideration**: Translates to Spring `@Service` classes with `@Autowired` dependencies, using RestTemplate or WebClient for external API calls.

### `src/models/`
**Purpose**: Data structure definitions using Pydantic
**Key Models**:
- **`aava_models.py`**: Core response models, exception classes (`DAResponse`, `DAValueError`, `DAServerError`)
- **`brainstormer_models.py`**: Pipeline-specific models (`MarketResearch`, `LeanBusinessCanvas`, `UserPersona`, `SwotAnalysis`, `FeaturesByCategory`)
- **`common.py`**: Shared data structures

**Validation**: Built-in Pydantic validation with custom field constraints
**Serialization**: Automatic JSON serialization/deserialization

**Java Mapping Consideration**: Translates to Java POJOs with Bean Validation annotations (`@NotNull`, `@Size`, `@Valid`), Jackson for JSON processing, and custom exception classes extending `RuntimeException`.

### `src/database/schema.sql`
**Database Type**: PostgreSQL with JSONB support
**Schema Structure**:
- **`aava_prod_studio.projects`**: Main project table with UUID run_id, metadata JSONB
- **Pipeline Step Tables**: `market_research`, `lean_business_canvas`, `user_personas`, `swot_analysis`, `features`, `roadmap_tasks`
- **`project_conversations`**: Chat history with message ordering
- **Foreign Key Relationships**: All tables reference projects(id) with CASCADE delete
- **JSONB Columns**: Extensive use for flexible data storage (tags, metadata, arrays)

**Java Mapping Consideration**: JPA entities with `@Entity`, `@Table`, `@Column` annotations. JSONB columns mapped using custom converters or Hibernate JSON types. Flyway/Liquibase for schema migration.

### `src/auth/` & `src/security/`
**Authentication Mechanism**:
- **JWT Token Validation**: Custom middleware validates `access-key` header
- **User Details Decryption**: Encrypted user information in JWT payload
- **Public Route Exemption**: Health checks and docs excluded from auth

**Key Components**:
- **`jwt_auth_middleware.py`**: FastAPI middleware for request interception
- **`jwt_decoder.py`**: JWT parsing and user extraction
- **`user_details_decryptor.py`**: Decrypts user information from token
- **`auth_helper.py`**: Dependency injection for authenticated user

**Java Mapping Consideration**: Spring Security with JWT filter chain, `@PreAuthorize` annotations, custom `UserDetailsService`, and JWT token provider beans.

### `src/config/settings.py`
**Configuration Management**:
- **Pydantic Settings**: Environment variable binding with type validation
- **Runtime Configuration**: Fetches config from external API on startup
- **Database Settings**: Connection parameters with fallback to environment
- **External Service URLs**: AI agent endpoints, Redis URL, access keys

**Key Features**:
- **Dynamic Configuration**: Updates settings from config service API
- **Environment Fallback**: Uses `.env` file and OS environment variables
- **Validation**: Pydantic field validation with defaults

**Java Mapping Consideration**: Spring Boot `@ConfigurationProperties` classes, `application.yml` configuration, `@Value` annotations, and Spring Cloud Config for external configuration.

### `src/utils/`
**Key Utility Modules**:
- **`db_connection_manager.py`**: Enhanced connection pooling with retry logic and monitoring
- **`logger.py`**: Centralized logging configuration
- **`api_helper/api_client.py`**: HTTP client wrapper with error handling
- **`decorators/avaplus_decorator.py`**: Custom decorators for cross-cutting concerns
- **`fragment_processor.py`**: Text processing utilities

**Database Connection Management**:
- **Connection Pooling**: AsyncPG pool with configurable min/max connections
- **Retry Logic**: Automatic retry on connection failures
- **Health Monitoring**: Connection statistics and cleanup operations
- **Timeout Handling**: Configurable timeouts for connection acquisition

**Java Mapping Consideration**: Spring Boot auto-configuration for connection pooling (HikariCP), custom utility classes, AOP for cross-cutting concerns, and Apache Commons Lang/Guava for utilities.

## 3. Data Flow & Execution Sequence

### Request Lifecycle
1. **Entry Point**: Request hits FastAPI application through Uvicorn ASGI server
2. **CORS Middleware**: Handles cross-origin requests and preflight
3. **JWT Authentication**: Validates access-key header and extracts user details
4. **Route Handler**: Processes request in `brainstormer_route.py`
5. **Service Layer**: Business logic in `aava_service.py` classes
6. **External API Calls**: Communication with AI agent services
7. **Database Operations**: Direct SQL queries via AsyncPG
8. **State Management**: Redis operations for pipeline state
9. **Response Formation**: Pydantic model serialization to JSON

### Data Transformation
- **Request Payload → Service Model**: Pydantic validation and transformation
- **Service Model → Database Entity**: Manual SQL query construction
- **Database Result → Response Model**: Row-to-model mapping
- **AI Service Response → Pipeline Data**: JSON parsing and validation

## 4. External Dependencies & Environment

### `pyproject.toml` Dependencies
**Core Framework**:
- `fastapi[standard]>=0.115.11` - Web framework
- `uvicorn==0.34.0` - ASGI server
- `starlette==0.47.2` - ASGI framework base

**Database & Caching**:
- `asyncpg==0.30.0` - PostgreSQL async driver
- `redis[hiredis]>=6.2.0` - Redis async client

**HTTP & API**:
- `aiohttp>=3.12.14` - HTTP client
- `python-multipart>=0.0.20` - Form data handling

**Security & Auth**:
- `pyjwt==2.10.1` - JWT token handling
- `cryptography>=45.0.6` - Cryptographic operations
- `pycryptodome>=3.23.0` - Additional crypto support

**Data Processing**:
- `pydantic-settings>=2.10.1` - Configuration management
- `pandas==2.2.3` - Data manipulation
- `beautifulsoup4==4.12.3` - HTML parsing
- `langchain==0.3.19` - AI/LLM framework

**Development Tools**:
- `pytest==8.3.5` - Testing framework
- `ruff==0.9.9` - Linting and formatting
- `pre-commit>=3.6.0` - Git hooks

### Environment Variables
**Database Configuration**:
- `DB_HOST`, `DB_PORT`, `DB_USER`, `DB_PASSWORD`, `DB_NAME`

**External Services**:
- `APP_CONFIG_API_URL` - Configuration service endpoint
- `PIPELINE_AGENT_SERVICE_ENDPOINT` - AI pipeline service
- `INDIVIDUAL_AGENT_SERVICE_ENDPOINT` - AI chat service
- `REDIS_URL` - Redis connection string
- `ACCESS_KEY` - API authentication key

### Docker Configuration
**Base Image**: `python:3.13.7-slim`
**Package Manager**: UV for fast dependency resolution
**Security**: Non-root user (`michelangelo`)
**Health Check**: HTTP endpoint monitoring
**Port**: 8000 (HTTP)

### CI/CD (Azure Pipelines)
**Trigger**: Scheduled every 6 hours on development branch
**Stages**:
1. **Prepare**: Repository checkout and setup
2. **Build**: Docker image creation and push to ACR
3. **Deploy**: Helm chart deployment to Kubernetes

**Key Features**:
- **Container Registry**: Azure Container Registry integration
- **Kubernetes Deployment**: Helm-based deployment
- **Environment Management**: Variable groups for configuration
- **Git Integration**: Automated chart updates

## 5. Migration Considerations (Python to Java)

### Key Architectural Differences
**Framework Paradigm**:
- **Python FastAPI**: Async-first with automatic OpenAPI generation
- **Java Spring Boot**: Annotation-driven with servlet-based architecture (can use WebFlux for reactive)

**Concurrency Model**:
- **Python**: Native asyncio with async/await syntax
- **Java**: Thread-based with CompletableFuture, or reactive streams (Project Reactor)

**Dependency Injection**:
- **Python**: Function-based dependencies with FastAPI's `Depends()`
- **Java**: Annotation-based with `@Autowired`, `@Inject`, constructor injection

### Data Type Mapping
| Python Type | Java Equivalent | Notes |
|-------------|----------------|-------|
| `str` | `String` | Direct mapping |
| `int` | `Integer`/`Long` | Consider size requirements |
| `float` | `Double`/`BigDecimal` | Use BigDecimal for precision |
| `bool` | `Boolean` | Direct mapping |
| `dict` | `Map<String, Object>` | Or specific DTOs |
| `list` | `List<T>` | Generic type required |
| `None` | `null` | Optional<T> preferred |
| `UUID` | `java.util.UUID` | Direct mapping |
| `datetime` | `LocalDateTime`/`Instant` | Consider timezone handling |
| `JSONB` | `@JdbcTypeCode(SqlTypes.JSON)` | Hibernate 6+ support |

### Concurrency/Asynchronicity
**Python Async Pattern**:
```python
async def get_data():
    async with db_manager.get_connection() as conn:
        result = await conn.fetch("SELECT * FROM table")
    return result
```

**Java Equivalent Options**:
1. **Traditional Blocking**:
```java
@Transactional
public List<Entity> getData() {
    return repository.findAll();
}
```

2. **Reactive (WebFlux)**:
```java
public Flux<Entity> getData() {
    return repository.findAll();
}
```

3. **CompletableFuture**:
```java
@Async
public CompletableFuture<List<Entity>> getData() {
    return CompletableFuture.completedFuture(repository.findAll());
}
```

### Error Handling
**Python Exception Handling**:
```python
class DAValueError(BaseAPIException):
    def __init__(self, message: str, error_code: APIErrorCode):
        super().__init__(status_code=400, error_code=error_code, message=message)

try:
    result = await some_operation()
except DAValueError as e:
    raise HTTPException(status_code=e.status_code, detail=e.message)
```

**Java Exception Handling**:
```java
@ResponseStatus(HttpStatus.BAD_REQUEST)
public class BusinessLogicException extends RuntimeException {
    private final APIErrorCode errorCode;

    public BusinessLogicException(String message, APIErrorCode errorCode) {
        super(message);
        this.errorCode = errorCode;
    }
}

@ExceptionHandler(BusinessLogicException.class)
public ResponseEntity<ErrorResponse> handleBusinessLogic(BusinessLogicException e) {
    return ResponseEntity.badRequest().body(new ErrorResponse(e.getMessage(), e.getErrorCode()));
}
```

### Testing Frameworks
**Python Testing (Pytest)**:
- **Async Support**: `pytest-asyncio` for async test functions
- **Fixtures**: Dependency injection for test setup
- **Mocking**: `pytest-mock` for service mocking
- **Database Testing**: `pytest-postgresql` for test database

**Java Testing Equivalents**:
- **JUnit 5**: `@Test`, `@ParameterizedTest`, `@TestInstance`
- **Spring Boot Test**: `@SpringBootTest`, `@WebMvcTest`, `@DataJpaTest`
- **Mockito**: `@Mock`, `@MockBean` for service mocking
- **Testcontainers**: For integration testing with real databases

### Build Tools
**Python (UV/PyProject)**:
- **Dependency Management**: `pyproject.toml` with version constraints
- **Lock File**: `uv.lock` for reproducible builds
- **Virtual Environment**: Automatic environment management
- **Scripts**: Custom commands in pyproject.toml

**Java Equivalents**:
- **Maven**: `pom.xml` with dependency management
- **Gradle**: `build.gradle` with Kotlin DSL option
- **Dependency Locking**: Gradle dependency locking or Maven lock files
- **Build Profiles**: Environment-specific configurations

## 6. Recommendations for Java Implementation

### Suggested Java Frameworks
**Primary Recommendation: Spring Boot 3.x**
- **Rationale**: Mature ecosystem, excellent async support with WebFlux, comprehensive security, extensive documentation
- **Reactive Support**: Spring WebFlux for async processing similar to FastAPI
- **Database**: Spring Data JPA with R2DBC for reactive database access
- **Security**: Spring Security with JWT support
- **Configuration**: Spring Boot configuration properties

**Alternative: Quarkus**
- **Benefits**: Faster startup, lower memory footprint, native compilation
- **Reactive**: Built-in reactive support with Mutiny
- **Database**: Hibernate Reactive with Panache
- **Considerations**: Smaller ecosystem, newer framework

### Database Access
**Recommended: Spring Data JPA + R2DBC**
- **JPA Entities**: For complex relationships and CRUD operations
- **R2DBC**: For reactive database access matching Python's async pattern
- **Custom Repositories**: For complex queries similar to current SQL approach
- **JSONB Support**: Hibernate 6+ native JSON support or custom converters

**Alternative: jOOQ**
- **Benefits**: Type-safe SQL, similar to current direct SQL approach
- **Code Generation**: Database-first approach with generated classes
- **Reactive Support**: jOOQ with R2DBC for async operations

### Security Framework
**Spring Security with JWT**
- **JWT Filter**: Custom filter for token validation
- **Method Security**: `@PreAuthorize` for endpoint protection
- **User Details**: Custom UserDetailsService for user information
- **CORS Configuration**: Declarative CORS setup

### Logging Framework
**SLF4J with Logback**
- **Structured Logging**: JSON format for cloud environments
- **Async Appenders**: Non-blocking logging for performance
- **MDC Support**: Request correlation IDs
- **Configuration**: Logback XML for environment-specific settings

### Dependency Injection
**Spring Framework DI**
- **Constructor Injection**: Preferred over field injection
- **Configuration Classes**: `@Configuration` for bean definitions
- **Profiles**: Environment-specific bean configurations
- **Conditional Beans**: `@ConditionalOnProperty` for feature flags

### Additional Recommendations
**HTTP Client**: WebClient (reactive) or RestTemplate (blocking)
**Validation**: Bean Validation with custom validators
**Documentation**: SpringDoc OpenAPI for automatic API documentation
**Monitoring**: Micrometer with Prometheus metrics
**Caching**: Spring Cache with Redis backend
**Testing**: TestContainers for integration tests with real PostgreSQL and Redis

### Migration Strategy
1. **Phase 1**: Core API structure with Spring Boot and basic endpoints
2. **Phase 2**: Database layer with JPA entities and repositories
3. **Phase 3**: Security implementation with JWT authentication
4. **Phase 4**: External service integration and async processing
5. **Phase 5**: Redis integration and state management
6. **Phase 6**: Comprehensive testing and performance optimization

This documentation provides a comprehensive foundation for migrating the AAVA Product Studio API Service from Python to Java while maintaining functionality and improving upon the existing architecture.
