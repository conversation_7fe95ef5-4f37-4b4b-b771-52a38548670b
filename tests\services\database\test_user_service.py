"""
Tests for UserService database operations.
"""

import pytest
import asyncpg
from uuid import UUID, uuid4
from src.services.database.user_service import UserService


@pytest.mark.asyncio
class TestUserService:
    """Test class for UserService."""

    @pytest.fixture
    async def user_service(self, test_db_pool: asyncpg.Pool):
        """Create UserService instance for testing."""
        return UserService(test_db_pool)

    async def test_create_user(self, user_service: UserService, clean_db):
        """Test creating a new user."""
        # Test data
        email = "<EMAIL>"
        full_name = "Test User"

        # Create user
        result = await user_service.create_user(email=email, full_name=full_name)

        # Assertions
        assert result is not None
        assert "user_id" in result
        assert result["email"] == email
        assert result["full_name"] == full_name

        # Verify user_id is a valid UUID
        user_id = UUID(result["user_id"])
        assert isinstance(user_id, UUID)

    async def test_create_user_without_full_name(
        self, user_service: UserService, clean_db
    ):
        """Test creating a user without full name."""
        email = "<EMAIL>"

        result = await user_service.create_user(email=email)

        assert result is not None
        assert result["email"] == email
        assert result["full_name"] is None

    async def test_create_user_duplicate_email(
        self, user_service: UserService, clean_db
    ):
        """Test creating a user with duplicate email should fail."""
        email = "<EMAIL>"

        # Create first user
        await user_service.create_user(email=email, full_name="User 1")

        # Try to create second user with same email
        with pytest.raises(Exception):  # Should raise unique constraint violation
            await user_service.create_user(email=email, full_name="User 2")

    async def test_get_user_by_id(self, user_service: UserService, clean_db):
        """Test retrieving user by ID."""
        # Create user first
        created_user = await user_service.create_user(
            email="<EMAIL>", full_name="Get Test User"
        )
        user_id = UUID(created_user["user_id"])

        # Get user by ID
        result = await user_service.get_user_by_id(user_id)

        # Assertions
        assert result is not None
        assert result["user_id"] == created_user["user_id"]
        assert result["email"] == "<EMAIL>"
        assert result["full_name"] == "Get Test User"
        assert "created_at" in result

    async def test_get_user_by_id_not_found(self, user_service: UserService, clean_db):
        """Test retrieving non-existent user by ID."""
        non_existent_id = uuid4()
        result = await user_service.get_user_by_id(non_existent_id)
        assert result is None

    async def test_get_user_by_email(self, user_service: UserService, clean_db):
        """Test retrieving user by email."""
        # Create user first
        email = "<EMAIL>"
        created_user = await user_service.create_user(
            email=email, full_name="Email Test User"
        )

        # Get user by email
        result = await user_service.get_user_by_email(email)

        # Assertions
        assert result is not None
        assert result["user_id"] == created_user["user_id"]
        assert result["email"] == email
        assert result["full_name"] == "Email Test User"

    async def test_get_user_by_email_not_found(
        self, user_service: UserService, clean_db
    ):
        """Test retrieving non-existent user by email."""
        result = await user_service.get_user_by_email("<EMAIL>")
        assert result is None

    async def test_update_user(self, user_service: UserService, clean_db):
        """Test updating user information."""
        # Create user first
        created_user = await user_service.create_user(
            email="<EMAIL>", full_name="Original Name"
        )
        user_id = UUID(created_user["user_id"])

        # Update user
        result = await user_service.update_user(
            user_id=user_id, email="<EMAIL>", full_name="Updated Name"
        )

        # Assertions
        assert result is not None
        assert result["user_id"] == created_user["user_id"]
        assert result["email"] == "<EMAIL>"
        assert result["full_name"] == "Updated Name"

    async def test_update_user_partial(self, user_service: UserService, clean_db):
        """Test updating user with only some fields."""
        # Create user first
        created_user = await user_service.create_user(
            email="<EMAIL>", full_name="Original Name"
        )
        user_id = UUID(created_user["user_id"])

        # Update only full name
        result = await user_service.update_user(
            user_id=user_id, full_name="Partially Updated Name"
        )

        # Assertions
        assert result is not None
        assert result["email"] == "<EMAIL>"  # Should remain unchanged
        assert result["full_name"] == "Partially Updated Name"

    async def test_update_user_not_found(self, user_service: UserService, clean_db):
        """Test updating non-existent user."""
        non_existent_id = uuid4()
        result = await user_service.update_user(
            user_id=non_existent_id, full_name="Should Not Work"
        )
        assert result is None

    async def test_delete_user(self, user_service: UserService, clean_db):
        """Test deleting a user."""
        # Create user first
        created_user = await user_service.create_user(
            email="<EMAIL>", full_name="To Be Deleted"
        )
        user_id = UUID(created_user["user_id"])

        # Delete user
        result = await user_service.delete_user(user_id)
        assert result is True

        # Verify user is deleted
        get_result = await user_service.get_user_by_id(user_id)
        assert get_result is None

    async def test_delete_user_not_found(self, user_service: UserService, clean_db):
        """Test deleting non-existent user."""
        non_existent_id = uuid4()
        result = await user_service.delete_user(non_existent_id)
        assert result is False

    async def test_list_users(self, user_service: UserService, clean_db):
        """Test listing users."""
        # Create multiple users
        users_data = [
            ("<EMAIL>", "User 1"),
            ("<EMAIL>", "User 2"),
            ("<EMAIL>", "User 3"),
        ]

        created_users = []
        for email, name in users_data:
            user = await user_service.create_user(email=email, full_name=name)
            created_users.append(user)

        # List users
        result = await user_service.list_users()

        # Assertions
        assert len(result) == 3
        assert all("user_id" in user for user in result)
        assert all("email" in user for user in result)
        assert all("full_name" in user for user in result)
        assert all("created_at" in user for user in result)

        # Check that all created users are in the result
        result_emails = {user["email"] for user in result}
        expected_emails = {email for email, _ in users_data}
        assert result_emails == expected_emails

    async def test_list_users_with_pagination(
        self, user_service: UserService, clean_db
    ):
        """Test listing users with pagination."""
        # Create 5 users
        for i in range(5):
            await user_service.create_user(
                email=f"user{i}@example.com", full_name=f"User {i}"
            )

        # Test pagination
        page1 = await user_service.list_users(limit=2, offset=0)
        page2 = await user_service.list_users(limit=2, offset=2)
        page3 = await user_service.list_users(limit=2, offset=4)

        # Assertions
        assert len(page1) == 2
        assert len(page2) == 2
        assert len(page3) == 1  # Only 1 user left

        # Ensure no duplicates across pages
        all_user_ids = set()
        for page in [page1, page2, page3]:
            for user in page:
                assert user["user_id"] not in all_user_ids
                all_user_ids.add(user["user_id"])

    async def test_list_users_empty(self, user_service: UserService, clean_db):
        """Test listing users when no users exist."""
        result = await user_service.list_users()
        assert result == []
