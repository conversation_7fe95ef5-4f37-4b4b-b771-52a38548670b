# Database Services Tests

This directory contains comprehensive tests for all database services in the application.

## Test Structure

```
tests/
├── conftest.py                     # Test configuration and fixtures
├── test_integration.py             # Integration tests for complete workflows
├── services/
│   └── database/
│       ├── test_user_service.py    # Tests for UserService
│       └── test_project_service.py # Tests for ProjectService
└── utils/
    └── db_cleanup.py              # Database cleanup utilities
```

## What's Tested

### UserService Tests (`test_user_service.py`)
- ✅ Create user with and without full name
- ✅ Handle duplicate email constraints
- ✅ Get user by ID and email
- ✅ Update user information (full and partial updates)
- ✅ Delete user
- ✅ List users with pagination
- ✅ Handle non-existent users

### ProjectService Tests (`test_project_service.py`)
- ✅ Create project with custom and auto-generated run_id
- ✅ Get project by run_id
- ✅ Update and retrieve market research data with competitors
- ✅ Update and retrieve lean business canvas data
- ✅ Update and retrieve user personas (single and multiple)
- ✅ Update and retrieve SWOT analysis data
- ✅ Update and retrieve features data
- ✅ Update and retrieve roadmap tasks data
- ✅ Add and retrieve conversation messages with proper ordering
- ✅ Handle non-existent projects

### Integration Tests (`test_integration.py`)
- ✅ Complete project workflow from creation to cleanup
- ✅ Data update workflows
- ✅ Multiple projects data isolation
- ✅ Comprehensive data cleanup verification

### Database Cleanup Utilities (`db_cleanup.py`)
- ✅ Clean all tables respecting foreign key constraints
- ✅ Clean project-specific data
- ✅ Clean user-specific data
- ✅ Get table counts for debugging
- ✅ Verify clean state
- ✅ Selective data cleanup by type

## Running Tests

### Prerequisites

1. **PostgreSQL Database**: You need a PostgreSQL database for testing.

2. **Environment Variables** (optional):
   ```bash
   export TEST_DB_HOST=localhost
   export TEST_DB_PORT=5432
   export TEST_DB_USER=postgres
   export TEST_DB_PASSWORD=your_password
   export TEST_DB_NAME=product_studio_test
   ```

3. **Dependencies**: Install test dependencies:
   ```bash
   uv add --dev pytest-asyncio
   ```

### Quick Start with Docker

If you don't have PostgreSQL installed locally:

```bash
# Start a test PostgreSQL container
docker run --name test-postgres \
  -e POSTGRES_PASSWORD=test \
  -e POSTGRES_DB=product_studio_test \
  -p 5432:5432 \
  -d postgres

# Wait a few seconds for the database to start
sleep 5

# Run tests
python run_tests.py
```

### Running Tests

1. **Using the test runner script** (recommended):
   ```bash
   python run_tests.py
   ```

2. **Using pytest directly**:
   ```bash
   uv run pytest tests/ -v --asyncio-mode=auto
   ```

3. **Running specific test files**:
   ```bash
   uv run pytest tests/services/database/test_user_service.py -v
   uv run pytest tests/services/database/test_project_service.py -v
   uv run pytest tests/test_integration.py -v
   ```

4. **Running specific test methods**:
   ```bash
   uv run pytest tests/services/database/test_user_service.py::TestUserService::test_create_user -v
   ```

## Test Features

### Automatic Database Cleanup
- Each test runs with a clean database state
- The `clean_db` fixture automatically cleans all tables before and after each test
- No test data persists between test runs

### Comprehensive Data Verification
- Tests verify that data is correctly written to the database
- Tests verify that data can be properly read back
- Tests verify that data is properly deleted/cleaned up
- Tests check foreign key relationships and constraints

### Realistic Test Data
- Uses realistic sample data for all entities
- Tests edge cases and error conditions
- Verifies data integrity across related tables

### Performance Considerations
- Tests use database transactions for cleanup
- Connection pooling is used for efficiency
- Tests are designed to run quickly while being thorough

## Test Data

The tests use comprehensive sample data defined in `conftest.py`:

- **Users**: Email, full name, timestamps
- **Projects**: Name, description, run_id, status, metadata
- **Market Research**: Summary, gaps, competitors with details
- **Lean Business Canvas**: All 9 components with arrays
- **User Personas**: Detailed persona information including demographics, goals, pain points
- **Features**: MoSCoW prioritized features with justifications
- **Roadmap Tasks**: Quarterly tasks with priorities and durations
- **Conversations**: Multi-turn conversations by type

## Troubleshooting

### Database Connection Issues
- Ensure PostgreSQL is running and accessible
- Check environment variables are set correctly
- Verify the test database exists or can be created
- Check user permissions

### Test Failures
- Check that all dependencies are installed
- Ensure the database schema is up to date
- Verify no other processes are using the test database
- Check for any database constraint violations

### Performance Issues
- Ensure database has proper indexes (defined in schema.sql)
- Check connection pool settings
- Monitor database logs for slow queries

## Contributing

When adding new database functionality:

1. Add corresponding tests in the appropriate test file
2. Update fixtures in `conftest.py` if new sample data is needed
3. Add cleanup methods to `db_cleanup.py` if new tables are added
4. Update this README with new test coverage information

## Database Schema

The tests work with the complete database schema defined in `src/database/schema.sql`, including:

- `brainstormer.users` - User accounts
- `brainstormer.projects` - Main projects table
- `brainstormer.market_research` - Market research data
- `brainstormer.competitors` - Competitor information
- `brainstormer.lean_business_canvas` - Business canvas data
- `brainstormer.user_personas` - User persona definitions
- `brainstormer.swot_analysis` - SWOT analysis data
- `brainstormer.features` - Feature definitions
- `brainstormer.roadmap_tasks` - Roadmap and task planning
- `brainstormer.project_conversations` - Conversation history

All foreign key relationships and constraints are properly tested.
