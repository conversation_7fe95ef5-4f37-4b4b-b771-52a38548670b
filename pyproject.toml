[project]
name = "product-studio-server"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "aiofiles==24.1.0",
    "aiohttp>=3.12.14",
    "asyncpg==0.30.0",
    "beautifulsoup4==4.12.3",
    "bson>=0.5.10",
    "fastapi[standard]>=0.115.11",
    "cryptography>=45.0.6",
    "langchain==0.3.19",
    "pandas==2.2.3",
    "pycryptodome>=3.23.0",
    "pydantic-settings>=2.10.1",
    "pyjwt==2.10.1",
    "pytest==8.3.5",
    "python-dotenv==1.0.1",
    "python-multipart>=0.0.20",
    "redis[hiredis]>=6.2.0",
    "ruff==0.9.9",
    "starlette==0.47.2",
    "tenacity>=9.1.2",
    "uvicorn==0.34.0",
    "websockets>=15.0.1",
]

[dependency-groups]
dev = [
    "git-cliff>=2.9.1",
    "pre-commit>=3.6.0",
    "pip-audit>=2.6.1",
    "pytest-asyncio>=1.1.0",
    "pytest-postgresql>=7.0.2",
    "httpx>=0.28.1",
    "pytest-mock>=3.14.1",
]
