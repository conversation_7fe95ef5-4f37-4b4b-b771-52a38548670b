"""
Simple test to verify test setup works.
"""

import pytest
from uuid import uuid4


def test_simple_assertion():
    """Test that basic assertions work."""
    assert 1 + 1 == 2


def test_uuid_generation():
    """Test that UUID generation works."""
    test_uuid = uuid4()
    assert test_uuid is not None
    assert len(str(test_uuid)) == 36


@pytest.mark.asyncio
async def test_async_function():
    """Test that async functions work in tests."""

    async def async_add(a, b):
        return a + b

    result = await async_add(2, 3)
    assert result == 5


def test_imports():
    """Test that our services can be imported."""
    from src.services.database.user_service import UserService
    from src.services.database.project_service import ProjectService

    assert UserService is not None
    assert ProjectService is not None
