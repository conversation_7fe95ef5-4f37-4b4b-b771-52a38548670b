import asyncpg
from typing import Dict, Any, List, Optional
from uuid import UUID, uuid4

from src.utils.logger import AppLogger
from src.utils.db_connection_manager import DatabaseConnectionManager

logger = AppLogger(__name__).get_logger()


class ProjectService:
    def __init__(
        self,
        db_pool: asyncpg.Pool,
        db_manager: Optional[DatabaseConnectionManager] = None,
    ):
        self.db_pool = db_pool
        self.db_manager = db_manager or DatabaseConnectionManager(db_pool)

    def _parse_jsonb_field(self, value, default_value=None):
        """
        Helper method to parse JSONB fields that might be strings or already parsed objects.

        Args:
            value: The value to parse (could be string, list, dict, or None)
            default_value: Default value to return if value is None

        Returns:
            Parsed Python object (list, dict, or original value)
        """
        import json

        if value is not None:
            # If it's already a Python object (asyncpg automatically parses JSONB)
            if isinstance(value, (list, dict)):
                return value
            # If it's a string, parse it
            elif isinstance(value, str):
                try:
                    return json.loads(value)
                except (json.JSONDecodeError, TypeError):
                    return value
            else:
                return value
        else:
            return default_value if default_value is not None else []

    async def get_or_create_user(self, user_email: str) -> int:
        """
        Get an existing user by email or create a new one if not found.
        Uses the rbac.da_users table.

        Args:
            user_email: The email address of the user

        Returns:
            The user ID as an integer

        Raises:
            asyncpg.PostgresError: If there's a database error
        """
        try:
            async with self.db_manager.get_transaction() as conn:
                # Try to find the user in rbac.users
                user = await conn.fetchrow(
                    """
                    SELECT user_id FROM rbac.users
                    WHERE email = $1
                    """,
                    user_email,
                )

                if user:
                    user_id = user["user_id"]
                    logger.info(f"Found existing user with ID: {user_id}")
                    return user_id

                # User not found, create a new one
                user_name = user_email.split("@")[0]  # Simple username from email

                user_id = await conn.fetchval(
                    """
                    INSERT INTO rbac.users (user_name, email, created_at, updated_at, is_active)
                    VALUES ($1, $2, NOW(), NOW(), true)
                    RETURNING user_id
                    """,
                    user_name,
                    user_email,
                )

                logger.info(f"Created new user with ID: {user_id}")
                return user_id

        except asyncpg.PostgresError as e:
            logger.error(f"Database error while getting/creating user: {str(e)}")
            raise

    async def create_project(
        self,
        name: str,
        description: str,
        created_by: int,  # Changed from UUID to int
        run_id: Optional[UUID] = None,
    ) -> Dict[str, Any]:
        """Create a new project"""
        if not run_id:
            run_id = uuid4()

        async with self.db_manager.get_connection() as conn:
            project_id = await conn.fetchval(
                """
                INSERT INTO aava_prod_studio.projects (run_id, name, description, created_by)
                VALUES ($1, $2, $3, $4)
                RETURNING id
            """,
                run_id,
                name,
                description,
                created_by,
            )

            return {
                "id": project_id,
                "run_id": str(run_id),
                "name": name,
                "description": description,
                "created_by": int(created_by),
            }

    async def update_market_research(
        self,
        project_id: int,
        market_summary: str,
        identified_gaps: str,
        competitors: List[Dict],
    ) -> int:
        """Update market research data"""
        async with self.db_manager.get_transaction() as conn:
            # Upsert market research
            mr_id = await conn.fetchval(
                """
                    INSERT INTO aava_prod_studio.market_research (project_id, market_summary, identified_gaps)
                    VALUES ($1, $2, $3)
                    ON CONFLICT (project_id) DO UPDATE SET
                        market_summary = EXCLUDED.market_summary,
                        identified_gaps = EXCLUDED.identified_gaps,
                        updated_at = CURRENT_TIMESTAMP
                    RETURNING id
                """,
                project_id,
                market_summary,
                identified_gaps,
            )

            # Delete existing competitors and insert new ones
            await conn.execute(
                """
                    DELETE FROM aava_prod_studio.competitors WHERE market_research_id = $1
                """,
                mr_id,
            )

            for competitor in competitors:
                await conn.execute(
                    """
                        INSERT INTO aava_prod_studio.competitors (market_research_id, name, url, strengths)
                        VALUES ($1, $2, $3, $4)
                    """,
                    mr_id,
                    competitor.get("name"),
                    competitor.get("url"),
                    competitor.get("strengths"),
                )

            return mr_id

    async def update_lean_business_canvas(
        self, project_id: int, lbc_data: Dict[str, Any]
    ) -> int:
        """Update lean business canvas"""
        import json

        async with self.db_manager.get_connection() as conn:
            return await conn.fetchval(
                """
                INSERT INTO aava_prod_studio.lean_business_canvas (
                    project_id, problem, solution, key_partners, value_proposition,
                    customer_segments, revenue_streams, key_metrics, alternatives, solution_tenants
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
                ON CONFLICT (project_id) DO UPDATE SET
                    problem = EXCLUDED.problem,
                    solution = EXCLUDED.solution,
                    key_partners = EXCLUDED.key_partners,
                    value_proposition = EXCLUDED.value_proposition,
                    customer_segments = EXCLUDED.customer_segments,
                    revenue_streams = EXCLUDED.revenue_streams,
                    key_metrics = EXCLUDED.key_metrics,
                    alternatives = EXCLUDED.alternatives,
                    solution_tenants = EXCLUDED.solution_tenants,
                    updated_at = CURRENT_TIMESTAMP
                RETURNING id
            """,
                project_id,
                json.dumps(lbc_data.get("problem", [])),
                json.dumps(lbc_data.get("solution", [])),
                json.dumps(lbc_data.get("key_partners", [])),
                json.dumps(lbc_data.get("value_proposition", [])),
                json.dumps(lbc_data.get("customer_segments", [])),
                json.dumps(lbc_data.get("revenue_streams", [])),
                json.dumps(lbc_data.get("key_metrics", [])),
                json.dumps(lbc_data.get("alternatives", [])),
                json.dumps(lbc_data.get("solution_tenants", [])),
            )

    async def update_user_personas(
        self, project_id: int, personas: List[Dict[str, Any]]
    ) -> List[int]:
        """Update user personas"""
        import json

        async with self.db_manager.get_transaction() as conn:
            # Delete existing personas
            await conn.execute(
                "DELETE FROM aava_prod_studio.user_personas WHERE project_id = $1",
                project_id,
            )

            persona_ids = []
            for persona in personas:
                # Normalize gender value to lowercase to match database constraint
                gender = persona.get("gender")
                if gender:
                    gender = gender.lower()

                persona_id = await conn.fetchval(
                    """
                        INSERT INTO aava_prod_studio.user_personas (
                            project_id, name, role, age, education, status, location,
                            tech_literacy, avatar, quote, personality, pain_points,
                            goals, motivation, expectations, devices, gender
                        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17)
                        RETURNING id
                    """,
                    project_id,
                    persona.get("name"),
                    persona.get("role"),
                    persona.get("age"),
                    persona.get("education"),
                    persona.get("status"),
                    persona.get("location"),
                    persona.get("tech_literacy"),
                    persona.get("avatar"),
                    persona.get("quote"),
                    json.dumps(persona.get("personality", [])),
                    json.dumps(persona.get("pain_points", [])),
                    json.dumps(persona.get("goals", [])),
                    json.dumps(persona.get("motivation", [])),
                    json.dumps(persona.get("expectations", [])),
                    json.dumps(persona.get("devices", [])),
                    gender,
                )
                persona_ids.append(persona_id)

            return persona_ids

    async def update_swot_analysis(
        self, project_id: int, swot_data: Dict[str, Any]
    ) -> int:
        """Update SWOT analysis"""
        import json

        async with self.db_manager.get_connection() as conn:
            return await conn.fetchval(
                """
                INSERT INTO aava_prod_studio.swot_analysis (
                    project_id, strengths, weaknesses, opportunities, threats
                ) VALUES ($1, $2, $3, $4, $5)
                ON CONFLICT (project_id) DO UPDATE SET
                    strengths = EXCLUDED.strengths,
                    weaknesses = EXCLUDED.weaknesses,
                    opportunities = EXCLUDED.opportunities,
                    threats = EXCLUDED.threats,
                    updated_at = CURRENT_TIMESTAMP
                RETURNING id
            """,
                project_id,
                json.dumps(swot_data.get("strengths", [])),
                json.dumps(swot_data.get("weaknesses", [])),
                json.dumps(swot_data.get("opportunities", [])),
                json.dumps(swot_data.get("threats", [])),
            )

    async def update_features(
        self, project_id: int, features: List[Dict[str, Any]]
    ) -> List[int]:
        """Update features"""
        import json

        async with self.db_manager.get_transaction() as conn:
            await conn.execute(
                "DELETE FROM aava_prod_studio.features WHERE project_id = $1",
                project_id,
            )

            feature_ids = []
            for feature in features:
                feature_id = await conn.fetchval(
                    """
                        INSERT INTO aava_prod_studio.features (
                            project_id, category, title, description, tenant,
                            justification, tags, moscow_rank
                        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
                        RETURNING id
                    """,
                    project_id,
                    feature.get("category"),
                    feature.get("title"),
                    feature.get("description"),
                    feature.get("tenant"),
                    feature.get("justification"),
                    json.dumps(feature.get("tags", [])),
                    feature.get("moscow_rank"),
                )
                feature_ids.append(feature_id)

            return feature_ids

    async def update_roadmap_tasks(
        self, project_id: int, tasks: List[Dict[str, Any]]
    ) -> List[int]:
        """Update roadmap tasks"""
        async with self.db_manager.get_transaction() as conn:
            await conn.execute(
                "DELETE FROM aava_prod_studio.roadmap_tasks WHERE project_id = $1",
                project_id,
            )

            task_ids = []
            for task in tasks:
                task_id = await conn.fetchval(
                    """
                        INSERT INTO aava_prod_studio.roadmap_tasks (
                            project_id, task, description, priority, duration, quarter, long_description
                        ) VALUES ($1, $2, $3, $4, $5, $6, $7)
                        RETURNING id
                    """,
                    project_id,
                    task.get("task"),
                    task.get("description"),
                    task.get("priority"),
                    task.get("duration"),
                    task.get("quarter"),
                    task.get("long_description"),
                )
                task_ids.append(task_id)

            return task_ids

    async def get_project_by_run_id(self, run_id: UUID) -> Optional[Dict[str, Any]]:
        """Get project by run_id"""
        async with self.db_manager.get_connection() as conn:
            return await conn.fetchrow(
                """
                SELECT * FROM aava_prod_studio.projects WHERE run_id = $1
            """,
                run_id,
            )

    async def add_conversation_message(
        self, project_id: int, conversation_type: str, content: str, role: str
    ) -> int:
        """Add a conversation message"""
        async with self.db_manager.get_connection() as conn:
            # Get next message order
            next_order = await conn.fetchval(
                """
                SELECT COALESCE(MAX(message_order), 0) + 1
                FROM aava_prod_studio.project_conversations
                WHERE project_id = $1 AND conversation_type = $2
            """,
                project_id,
                conversation_type,
            )

            return await conn.fetchval(
                """
                INSERT INTO aava_prod_studio.project_conversations
                (project_id, conversation_type, content, role, message_order)
                VALUES ($1, $2, $3, $4, $5)
                RETURNING id
            """,
                project_id,
                conversation_type,
                content,
                role,
                next_order,
            )

    async def get_conversation_messages(
        self, project_id: int, conversation_type: str
    ) -> List[Dict[str, Any]]:
        """Get conversation messages for a project and conversation type"""
        async with self.db_manager.get_connection() as conn:
            return await conn.fetch(
                """
                SELECT content, role, message_order, created_at
                FROM aava_prod_studio.project_conversations
                WHERE project_id = $1 AND conversation_type = $2
                ORDER BY message_order ASC
            """,
                project_id,
                conversation_type,
            )

    async def get_market_research(self, project_id: int) -> Optional[Dict[str, Any]]:
        """Get market research data for a project"""
        async with self.db_manager.get_connection() as conn:
            # Get market research
            mr_data = await conn.fetchrow(
                """
                SELECT market_summary, identified_gaps
                FROM aava_prod_studio.market_research
                WHERE project_id = $1
                """,
                project_id,
            )

            if not mr_data:
                return None

            # Get competitors
            competitors = await conn.fetch(
                """
                SELECT c.name, c.url, c.strengths
                FROM aava_prod_studio.competitors c
                JOIN aava_prod_studio.market_research mr ON c.market_research_id = mr.id
                WHERE mr.project_id = $1
                """,
                project_id,
            )

            return {
                "market_summary": mr_data["market_summary"],
                "identified_gaps": mr_data["identified_gaps"],
                "competitors": [dict(comp) for comp in competitors],
            }

    async def get_lean_business_canvas(
        self, project_id: int
    ) -> Optional[Dict[str, Any]]:
        """Get lean business canvas data for a project with parsed JSONB fields"""
        import json

        async with self.db_manager.get_connection() as conn:
            lbc_data = await conn.fetchrow(
                """
                SELECT problem, solution, key_partners, value_proposition,
                       customer_segments, revenue_streams, key_metrics,
                       alternatives, solution_tenants
                FROM aava_prod_studio.lean_business_canvas
                WHERE project_id = $1
                """,
                project_id,
            )

            if not lbc_data:
                return None

            # Parse JSONB fields
            parsed_data = {}
            jsonb_fields = [
                "problem",
                "solution",
                "key_partners",
                "value_proposition",
                "customer_segments",
                "revenue_streams",
                "key_metrics",
                "alternatives",
                "solution_tenants",
            ]

            for field in jsonb_fields:
                value = lbc_data[field]
                if value is not None:
                    # If it's already a Python object (asyncpg automatically parses JSONB)
                    if isinstance(value, (list, dict)):
                        parsed_data[field] = value
                    # If it's a string, parse it
                    elif isinstance(value, str):
                        try:
                            parsed_data[field] = json.loads(value)
                        except (json.JSONDecodeError, TypeError):
                            parsed_data[field] = value
                    else:
                        parsed_data[field] = value
                else:
                    parsed_data[field] = []

            return parsed_data

    async def get_user_personas(self, project_id: int) -> Optional[Dict[str, Any]]:
        """Get user personas for a project with parsed JSONB fields"""
        import json

        async with self.db_manager.get_connection() as conn:
            personas = await conn.fetch(
                """
                SELECT name, role, age, gender, education, status, location,
                       tech_literacy, avatar, quote, personality, pain_points,
                       goals, motivation, expectations, devices
                FROM aava_prod_studio.user_personas
                WHERE project_id = $1
                """,
                project_id,
            )

            if not personas:
                return None

            # Parse JSONB fields for each persona
            jsonb_fields = [
                "personality",
                "pain_points",
                "goals",
                "motivation",
                "expectations",
                "devices",
            ]
            personas_list = []

            for persona in personas:
                persona_dict = dict(persona)

                # Parse JSONB fields
                for field in jsonb_fields:
                    value = persona_dict.get(field)
                    if value is not None:
                        # If it's already a Python object (asyncpg automatically parses JSONB)
                        if isinstance(value, (list, dict)):
                            persona_dict[field] = value
                        # If it's a string, parse it
                        elif isinstance(value, str):
                            try:
                                persona_dict[field] = json.loads(value)
                            except (json.JSONDecodeError, TypeError):
                                persona_dict[field] = value
                        else:
                            persona_dict[field] = value
                    else:
                        persona_dict[field] = []

                personas_list.append(persona_dict)

            return (
                {"personas": personas_list}
                if len(personas_list) > 1
                else personas_list[0]
            )

    async def get_swot_analysis(self, project_id: int) -> Optional[Dict[str, Any]]:
        """Get SWOT analysis for a project with parsed JSONB fields"""
        import json

        async with self.db_manager.get_connection() as conn:
            swot_data = await conn.fetchrow(
                """
                SELECT strengths, weaknesses, opportunities, threats
                FROM aava_prod_studio.swot_analysis
                WHERE project_id = $1
                """,
                project_id,
            )

            if not swot_data:
                return None

            # Parse JSONB fields
            parsed_data = {}
            jsonb_fields = ["strengths", "weaknesses", "opportunities", "threats"]

            for field in jsonb_fields:
                value = swot_data[field]
                if value is not None:
                    # If it's already a Python object (asyncpg automatically parses JSONB)
                    if isinstance(value, (list, dict)):
                        parsed_data[field] = value
                    # If it's a string, parse it
                    elif isinstance(value, str):
                        try:
                            parsed_data[field] = json.loads(value)
                        except (json.JSONDecodeError, TypeError):
                            parsed_data[field] = value
                    else:
                        parsed_data[field] = value
                else:
                    parsed_data[field] = []

            return parsed_data

    async def get_features(self, project_id: int) -> Optional[List[Dict[str, Any]]]:
        """Get features for a project with parsed JSONB fields"""
        import json

        async with self.db_manager.get_connection() as conn:
            features = await conn.fetch(
                """
                SELECT category, title, description, tenant,
                       justification, tags, moscow_rank
                FROM aava_prod_studio.features
                WHERE project_id = $1
                ORDER BY moscow_rank, category
                """,
                project_id,
            )

            if not features:
                return None

            # Parse JSONB fields for each feature
            features_list = []
            for feature in features:
                feature_dict = dict(feature)

                # Parse tags JSONB field
                tags_value = feature_dict.get("tags")
                if tags_value is not None:
                    # If it's already a Python object (asyncpg automatically parses JSONB)
                    if isinstance(tags_value, (list, dict)):
                        feature_dict["tags"] = tags_value
                    # If it's a string, parse it
                    elif isinstance(tags_value, str):
                        try:
                            feature_dict["tags"] = json.loads(tags_value)
                        except (json.JSONDecodeError, TypeError):
                            feature_dict["tags"] = tags_value
                    else:
                        feature_dict["tags"] = tags_value
                else:
                    feature_dict["tags"] = []

                features_list.append(feature_dict)

            return features_list

    async def get_roadmap_tasks(
        self, project_id: int
    ) -> Optional[List[Dict[str, Any]]]:
        """Get roadmap tasks for a project"""
        async with self.db_manager.get_connection() as conn:
            tasks = await conn.fetch(
                """
                SELECT task, description, priority, duration, quarter, long_description
                FROM aava_prod_studio.roadmap_tasks
                WHERE project_id = $1
                ORDER BY quarter, priority
                """,
                project_id,
            )

            return [dict(task) for task in tasks] if tasks else None

    async def get_all_project_details(
        self, project_id: int
    ) -> Optional[Dict[str, Any]]:
        """
        Get comprehensive project details including all related data.

        Args:
            project_id: The project ID to retrieve details for

        Returns:
            Dictionary containing all project details or None if project not found
        """
        async with self.db_manager.get_connection() as conn:
            # Get basic project information (without user join to avoid table existence issues)
            project = await conn.fetchrow(
                """
                SELECT p.id, p.run_id, p.name, p.description, p.created_by,
                       p.created_at, p.updated_at, p.status, p.tags, p.metadata
                FROM aava_prod_studio.projects p
                WHERE p.id = $1
                """,
                project_id,
            )

            if not project:
                return None

            # Convert project to dict and format with JSONB parsing
            import json

            project_details = dict(project)
            project_details["run_id"] = str(project_details["run_id"])

            # Parse project JSONB fields (tags and metadata)
            for field in ["tags", "metadata"]:
                value = project_details.get(field)
                if value is not None:
                    # If it's already a Python object (asyncpg automatically parses JSONB)
                    if isinstance(value, (list, dict)):
                        project_details[field] = value
                    # If it's a string, parse it
                    elif isinstance(value, str):
                        try:
                            project_details[field] = json.loads(value)
                        except (json.JSONDecodeError, TypeError):
                            project_details[field] = value
                    else:
                        project_details[field] = value
                else:
                    # Set default values
                    project_details[field] = [] if field == "tags" else {}

            # Get all related data
            market_research = await self.get_market_research(project_id)
            lean_business_canvas = await self.get_lean_business_canvas(project_id)
            user_personas = await self.get_user_personas(project_id)
            swot_analysis = await self.get_swot_analysis(project_id)
            features = await self.get_features(project_id)
            roadmap_tasks = await self.get_roadmap_tasks(project_id)

            # Get conversation messages for all types
            conversation_types = [
                "market_research",
                "lean_business_canvas",
                "user_personas",
                "swot_analysis",
                "features",
                "roadmap_tasks",
                "competitors",
            ]

            conversations = {}
            for conv_type in conversation_types:
                messages = await self.get_conversation_messages(project_id, conv_type)
                if messages:
                    conversations[conv_type] = [dict(msg) for msg in messages]

            # Compile comprehensive project details
            return {
                "project": project_details,
                "market_research": market_research,
                "lean_business_canvas": lean_business_canvas,
                "user_personas": user_personas,
                "swot_analysis": swot_analysis,
                "features": features,
                "roadmap_tasks": roadmap_tasks,
                "conversations": conversations,
            }

    async def get_all_project_details_by_run_id(
        self, run_id: UUID
    ) -> Optional[Dict[str, Any]]:
        """
        Get comprehensive project details by run_id.

        Args:
            run_id: The run_id to retrieve project details for

        Returns:
            Dictionary containing all project details or None if project not found
        """
        # First get the project to find the project_id
        project = await self.get_project_by_run_id(run_id)
        if not project:
            return None

        return await self.get_all_project_details(project["id"])

    async def get_all_projects_for_user(self, user_id: int) -> List[Dict[str, Any]]:
        """
        Get all projects created by a specific user with basic information.

        Args:
            user_id: The user ID to retrieve projects for

        Returns:
            List of project dictionaries with basic information
        """
        async with self.db_manager.get_connection() as conn:
            projects = await conn.fetch(
                """
                SELECT p.id, p.run_id, p.name, p.description, p.created_by,
                       p.created_at, p.updated_at, p.status, p.tags, p.metadata
                FROM aava_prod_studio.projects p
                WHERE p.created_by = $1
                ORDER BY p.updated_at DESC
                """,
                user_id,
            )

            # Convert to list of dicts and format run_id with JSONB parsing
            import json

            project_list = []
            for project in projects:
                project_dict = dict(project)
                project_dict["run_id"] = str(project_dict["run_id"])

                # Parse project JSONB fields (tags and metadata)
                for field in ["tags", "metadata"]:
                    value = project_dict.get(field)
                    if value is not None:
                        # If it's already a Python object (asyncpg automatically parses JSONB)
                        if isinstance(value, (list, dict)):
                            project_dict[field] = value
                        # If it's a string, parse it
                        elif isinstance(value, str):
                            try:
                                project_dict[field] = json.loads(value)
                            except (json.JSONDecodeError, TypeError):
                                project_dict[field] = value
                        else:
                            project_dict[field] = value
                    else:
                        # Set default values
                        project_dict[field] = [] if field == "tags" else {}

                project_list.append(project_dict)

            return project_list
