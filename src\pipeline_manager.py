import json
import asyncpg
from pydantic import ValidationError
from src.services.aava_service import PipelineAgentService, ConversationalAgentService
from src.services.database.project_service import ProjectService
from src.models.brainstormer_models import ConversationalResponse
from src.state_manager import RedisManager

from src.config.pipeline_config import PIP<PERSON>INE_STEPS, PIPELINE_CONTEXT_DEFINITION
from typing import Dict, Any

from src.utils.helpers.text_parser import get_content_inside_markdown
from src.utils.logger import AppLogger

logger = AppLogger("Api_Client_Logger").get_logger()


class PipelineManager:
    def __init__(
        self,
        redis_manager: RedisManager,
        pipeline_agent: PipelineAgentService,
        conversational_agent: ConversationalAgentService,
        db_pool: asyncpg.Pool,
        db_manager=None,
    ):
        self.redis = redis_manager
        self.pipeline_agent = pipeline_agent
        self.convo_agent = conversational_agent
        self.steps = PIPELINE_STEPS
        self.context_def = PIPELINE_CONTEXT_DEFINITION
        self.db_pool = db_pool

        # Only initialize project service if database is available
        if db_pool:
            self.project_service = ProjectService(db_pool, db_manager)
            # Inject project service into conversational agent
            self.convo_agent.set_project_service(self.project_service)
            self.pipeline_agent.set_project_service(self.project_service)
        else:
            self.project_service = None
            logger.warning("Database not available - running without persistence")

    async def _gather_context_for_step(
        self, run_id: str, step_name: str, additional_context: Dict = None
    ) -> Dict[str, Any]:
        """Loads required data from Redis for the given step, with database fallback."""
        logger.info(f"Gathering context for step '{step_name}' (run_id: {run_id})")

        context = {}
        if additional_context:
            context.update(additional_context)
            logger.debug(
                f"Added additional context keys: {list(additional_context.keys())}"
            )

        step_info = self.context_def.get(step_name, {})
        required_steps = step_info.get("context_needed", [])
        logger.info(f"Step '{step_name}' requires context from: {required_steps}")

        for required_step in required_steps:
            logger.debug(f"Loading data for required step: {required_step}")
            step_data = await self.redis.load_step_data(run_id, required_step)

            # Fallback to database if not in Redis
            if not step_data:
                logger.info(
                    f"Step '{required_step}' not found in Redis, falling back to database"
                )
                step_data = await self._load_step_from_db(run_id, required_step)
                # Cache in Redis for future use
                if step_data:
                    await self.redis.save_step_data(run_id, required_step, step_data)
                    logger.debug(f"Cached step '{required_step}' data in Redis")
                else:
                    logger.warning(
                        f"No data found for step '{required_step}' in database either"
                    )
            else:
                logger.debug(f"Found step '{required_step}' data in Redis")

            if step_data:
                # If specific keys are defined, only take those
                specific_keys = step_info.get("context_keys", {}).get(required_step)
                if specific_keys:
                    context[required_step] = {
                        k: step_data.get(k) for k in specific_keys
                    }
                    logger.debug(
                        f"Using specific keys {specific_keys} for step '{required_step}'"
                    )
                else:
                    context[required_step] = step_data
                    logger.debug(f"Using all data for step '{required_step}'")

        logger.info(
            f"Context gathering complete. Final context keys: {list(context.keys())}"
        )
        return context

    async def _load_step_from_db(self, run_id: str, step_name: str) -> Dict[str, Any]:
        """Load step data from database"""
        if not self.project_service:
            logger.warning("Database not available - cannot load step from database")
            return None

        try:
            logger.debug(f"Loading step '{step_name}' from database (run_id: {run_id})")

            project = await self.project_service.get_project_by_run_id(run_id)
            if not project:
                logger.warning(f"No project found for run_id: {run_id}")
                return None
        except Exception as e:
            logger.error(f"Database error loading step '{step_name}': {e}")
            return None

        project_id = project["id"]
        logger.debug(f"Found project_id: {project_id} for run_id: {run_id}")

        if step_name == "market_research":
            data = await self.project_service.get_market_research(project_id)
        elif step_name == "lbc":
            data = await self.project_service.get_lean_business_canvas(project_id)
        elif step_name == "persona":
            data = await self.project_service.get_user_personas(project_id)
        elif step_name == "swot":
            data = await self.project_service.get_swot_analysis(project_id)
        elif step_name == "features":
            data = await self.project_service.get_features(project_id)
        elif step_name == "roadmap":
            data = await self.project_service.get_roadmap_tasks(project_id)
        else:
            logger.warning(f"Unknown step name: {step_name}")
            return None

        if data:
            logger.info(f"Successfully loaded step '{step_name}' from database")
        else:
            logger.info(f"No data found for step '{step_name}' in database")

        return data

    async def start_new_run(
        self, run_id: str, user_idea: str, user_signature: str
    ) -> Dict[str, Any]:
        """Starts the first step of a new pipeline run."""
        first_step = self.steps[0]

        # Save the user request for later steps
        await self.redis.save_step_data(run_id, "user_request", user_idea)

        context = await self._gather_context_for_step(
            run_id, first_step, {"user_request": user_idea}
        )

        generated_data = await self.pipeline_agent.generate_market_research(
            first_step, context, user_signature
        )

        await self.redis.save_step_data(run_id, first_step, generated_data)

        # Persist to database after successful generation
        await self._persist_step_to_db(run_id, first_step, generated_data)

        return generated_data

    async def trigger_next_step(
        self, run_id: str, current_step: str, user_signature: str
    ) -> Dict[str, Any]:
        """Triggers the next step in the defined pipeline sequence."""
        try:
            current_index = self.steps.index(current_step)
            if current_index + 1 >= len(self.steps):
                raise ValueError("Already at the last step of the pipeline.")
            next_step = self.steps[current_index + 1]
        except ValueError:
            raise ValueError(f"Invalid current step: {current_step}")

        # Get the original user request from the first step
        user_request_data = await self.redis.load_step_data(run_id, "user_request")
        additional_context = (
            {"user_request": user_request_data} if user_request_data else {}
        )

        context = await self._gather_context_for_step(
            run_id, next_step, additional_context
        )

        generated_data = await self.pipeline_agent.generate_artifact(
            next_step, context, user_signature
        )
        await self.redis.save_step_data(run_id, next_step, generated_data)

        # Persist to database after successful generation
        await self._persist_step_to_db(run_id, next_step, generated_data)

        return {"run_id": run_id, "step": next_step, "data": generated_data}

    async def handle_chat_message(
        self, run_id: str, current_step: str, user_message: str, user_signature: str
    ) -> Dict[str, Any]:
        """Processes a user's chat message and takes appropriate action."""
        # Validate step name
        if current_step not in self.steps:
            raise ValueError(
                f"Invalid step name: {current_step}. Valid steps are: {self.steps}"
            )

        current_state = await self.redis.load_step_data(run_id, current_step)

        # Fallback to database if not in Redis
        if not current_state:
            current_state = await self._load_step_from_db(run_id, current_step)
            # Cache in Redis for future use
            if current_state:
                await self.redis.save_step_data(run_id, current_step, current_state)

        if not current_state:
            raise ValueError(
                f"No data found for run_id '{run_id}' and step '{current_step}'. Please ensure the pipeline step has been completed first."
            )

        try:
            # Get response from conversational agent with improved error handling
            response_data = await self.convo_agent.get_response(
                user_message, current_state, run_id, current_step, user_signature
            )

            # Validate the response structure
            try:
                response = ConversationalResponse(**response_data)
            except ValidationError as e:
                # Log the validation error and create a fallback response
                print(f"AI response validation error: {e}")
                print(f"Response data: {response_data}")

                # Create a fallback response
                response = ConversationalResponse(
                    response_type="clarification",
                    message_to_user=f"I received your message: '{user_message}', but I'm having trouble processing it right now. Please try rephrasing your request.",
                    payload={},
                )

        except Exception as e:
            print(f"Error getting response from conversational agent: {e}")
            # Create a fallback response for any other errors
            response = ConversationalResponse(
                response_type="clarification",
                message_to_user=f"I'm currently experiencing technical difficulties. Your message '{user_message}' was received, but I cannot process it at the moment. Please try again later.",
                payload={},
            )

        # Handle different response types
        if response.response_type == "edit":
            await self.redis.save_step_data(run_id, current_step, response.payload)
            # Persist edited data to database
            await self._persist_step_to_db(run_id, current_step, response.payload)
            # Save conversation to database
            await self._save_conversation_to_db(
                run_id, current_step, user_message, response.model_dump()
            )

        elif response.response_type == "delegate_search":
            try:
                query = response.payload.get("query")
                search_results = await self.pipeline_agent.perform_web_search(
                    query, user_signature
                )
                search_results = search_results.get("content", "")
                extracted_json = get_content_inside_markdown(search_results, "json")
                search_results = json.loads(extracted_json)

                await self.redis.save_step_data(run_id, current_step, search_results)

                # Make the second call to the convo agent with the search results
                final_response_data = await self.convo_agent.get_response(
                    user_message,
                    current_state,
                    run_id,
                    current_step,
                    user_signature,
                    search_results=search_results,
                )
                response = ConversationalResponse(**final_response_data)

                if response.response_type == "edit":
                    await self.redis.save_step_data(
                        run_id, current_step, response.payload
                    )
                    # Persist final edited data to database
                    await self._persist_step_to_db(
                        run_id, current_step, response.payload
                    )

            except Exception as e:
                print(f"Error during search delegation: {e}")
                # Fallback to clarification response
                response = ConversationalResponse(
                    response_type="clarification",
                    message_to_user="I tried to search for information but encountered an issue. Please try rephrasing your request.",
                    payload={},
                )

            # Save entire conversation thread to database
            await self._save_conversation_to_db(
                run_id, current_step, user_message, response.model_dump()
            )

        return response.model_dump()

    async def _persist_step_to_db(
        self, run_id: str, step_name: str, data: Dict[str, Any]
    ):
        """Persist step data to database"""
        if not self.project_service:
            logger.debug("Database not available - skipping persistence")
            return

        try:
            project = await self.project_service.get_project_by_run_id(run_id)
            if not project:
                logger.warning(
                    f"No project found for run_id: {run_id} - skipping persistence"
                )
                return

            project_id = project["id"]
        except Exception as e:
            logger.error(f"Database error persisting step '{step_name}': {e}")
            return

        if step_name == "market_research":
            await self.project_service.update_market_research(
                project_id,
                data.get("market_summary", ""),
                data.get("identified_gaps", ""),
                data.get("competitors", []),
            )
        elif step_name == "lbc":
            await self.project_service.update_lean_business_canvas(project_id, data)
        elif step_name == "persona":
            # Handle both single persona and persona collection
            personas_data = (
                data.get("personas", [data]) if "personas" in data else [data]
            )
            await self.project_service.update_user_personas(project_id, personas_data)
        elif step_name == "swot":
            await self.project_service.update_swot_analysis(project_id, data)
        elif step_name == "features":
            # Handle features by category or flat list
            features_list = []
            if "must_have" in data or "should_have" in data:
                # Features organized by category
                for category in ["must_have", "should_have", "could_have", "wont_have"]:
                    for feature in data.get(category, []):
                        feature["category"] = category
                        features_list.append(feature)
            else:
                # Flat features list
                features_list = (
                    data if isinstance(data, list) else data.get("features", [])
                )

            await self.project_service.update_features(project_id, features_list)
        elif step_name == "roadmap":
            # Handle roadmap tasks
            tasks = data.get("project_tasks", data.get("roadmap", []))
            if isinstance(tasks, dict):
                tasks = tasks.get("tasks", [])
            await self.project_service.update_roadmap_tasks(project_id, tasks)

    async def _save_conversation_to_db(
        self, run_id: str, step_name: str, user_message: str, response: Dict
    ):
        """Save conversation messages to database"""
        if not self.project_service:
            logger.debug("Database not available - skipping conversation save")
            return

        try:
            project = await self.project_service.get_project_by_run_id(run_id)
            if not project:
                logger.warning(
                    f"No project found for run_id: {run_id} - skipping conversation save"
                )
                return

            project_id = project["id"]
            await self.project_service.add_conversation_message(
                project_id, step_name, user_message, "user"
            )
            await self.project_service.add_conversation_message(
                project_id, step_name, response["message_to_user"], "assistant"
            )
        except Exception as e:
            logger.error(f"Database error saving conversation: {e}")
            return

    async def manage_step_data(
        self,
        run_id: str,
        step: str,
        operation_type: str,
        payload: Dict[str, Any],
        user_signature: str,
    ) -> Dict[str, Any]:
        """Manage pipeline step data - update, delete, or add."""

        # Validate step name
        if step not in self.steps:
            raise ValueError(
                f"Invalid step name: {step}. Valid steps are: {self.steps}"
            )

        # Validate operation type
        if operation_type not in ["update", "delete", "add"]:
            raise ValueError(
                f"Invalid operation type: {operation_type}. Valid types are: update, delete, add"
            )

        try:
            # Get current step data
            current_data = await self.redis.load_step_data(run_id, step)

            # Fallback to database if not in Redis
            if not current_data:
                current_data = await self._load_step_from_db(run_id, step)
                if current_data:
                    await self.redis.save_step_data(run_id, step, current_data)

            # Initialize empty data if none exists
            if not current_data:
                current_data = {}

            # Perform the operation
            updated_data = self._perform_data_operation(
                current_data, operation_type, payload
            )

            # Save to Redis
            await self.redis.save_step_data(run_id, step, updated_data)

            # Persist to database
            await self._persist_step_to_db(run_id, step, updated_data)

            return {
                "success": True,
                "message": f"Successfully {operation_type}d data for step {step}",
                "data": updated_data,
            }

        except Exception as e:
            logger.error(f"Error managing step data: {e}")
            return {
                "success": False,
                "message": f"Failed to {operation_type} data: {str(e)}",
                "data": None,
            }

    def _perform_data_operation(
        self, current_data: Dict[str, Any], operation_type: str, payload: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Perform the actual data operation."""

        if operation_type == "update":
            # Deep merge the payload into current data
            return self._deep_merge(current_data, payload)

        elif operation_type == "add":
            # Add new items to arrays or merge new fields
            updated_data = current_data.copy()
            for key, value in payload.items():
                if (
                    key in updated_data
                    and isinstance(updated_data[key], list)
                    and isinstance(value, list)
                ):
                    updated_data[key].extend(value)
                elif (
                    key in updated_data
                    and isinstance(updated_data[key], list)
                    and not isinstance(value, list)
                ):
                    updated_data[key].append(value)
                else:
                    updated_data[key] = value
            return updated_data

        elif operation_type == "delete":
            # Remove specified fields or items
            updated_data = current_data.copy()
            for key, value in payload.items():
                if key in updated_data:
                    if isinstance(updated_data[key], list) and isinstance(value, list):
                        # Remove specific items from list
                        updated_data[key] = [
                            item for item in updated_data[key] if item not in value
                        ]
                    elif isinstance(updated_data[key], list) and not isinstance(
                        value, list
                    ):
                        # Remove single item from list
                        if value in updated_data[key]:
                            updated_data[key].remove(value)
                    else:
                        # Remove entire field
                        del updated_data[key]
            return updated_data

        return current_data

    def _deep_merge(
        self, dict1: Dict[str, Any], dict2: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Deep merge two dictionaries."""
        result = dict1.copy()
        for key, value in dict2.items():
            if (
                key in result
                and isinstance(result[key], dict)
                and isinstance(value, dict)
            ):
                result[key] = self._deep_merge(result[key], value)
            else:
                result[key] = value
        return result
