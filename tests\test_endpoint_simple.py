#!/usr/bin/env python3
"""
Simple test script to verify the /pipeline/manage-data endpoint works.
"""

import asyncio
import json
import httpx


async def test_manage_data_endpoint():
    """Test the manage data endpoint with a simple request."""

    base_url = "http://localhost:8000/server/product"
    headers = {
        "access-key": "Bearer test-jwt-token",
        "Content-Type": "application/json",
    }

    # Test data
    request_data = {
        "run_id": "test-run-123",
        "step": "market_research",
        "operation_type": "update",
        "payload": {
            "market_summary": "Test market summary update",
            "identified_gaps": "Test gaps update",
        },
    }

    print("🧪 Testing /pipeline/manage-data endpoint")
    print("=" * 50)
    print(f"URL: {base_url}/api/v1/pipeline/manage-data")
    print(f"Request: {json.dumps(request_data, indent=2)}")
    print()

    async with httpx.AsyncClient() as client:
        try:
            # First check if server is available
            heartbeat_response = await client.get(f"{base_url}/heartbeat")
            if heartbeat_response.status_code != 200:
                print("❌ Server is not available")
                return

            print("✅ Server is available")

            # Test the endpoint
            response = await client.post(
                f"{base_url}/api/v1/pipeline/manage-data",
                json=request_data,
                headers=headers,
                timeout=30.0,
            )

            print(f"Response Status: {response.status_code}")
            print(f"Response Body: {json.dumps(response.json(), indent=2)}")

            if response.status_code == 200:
                print("✅ Test passed!")
            else:
                print("❌ Test failed!")

        except Exception as e:
            print(f"❌ Error: {e}")


if __name__ == "__main__":
    asyncio.run(test_manage_data_endpoint())
