# src/utils/app_logger.py
import logging

# Dictionary to keep track of configured loggers
_configured_loggers = {}

class CustomFormatter(logging.Formatter):
    """Custom formatter for better log readability"""

    # Define colors for different log levels
    COLORS = {
        'DEBUG': '\033[36m',     # Cyan
        'INFO': '\033[32m',      # Green
        'WARNING': '\033[33m',   # Yellow
        'ERROR': '\033[31m',     # Red
        'CRITICAL': '\033[41m',  # Red background
        'RESET': '\033[0m'       # Reset color
    }

    def __init__(self, use_colors=True):
        self.use_colors = use_colors
        # Format: timestamp | level | logger | message
        super().__init__('%(asctime)s | %(levelname)-8s | %(name)-15s | %(message)s')

    def format(self, record):
        # Save the original format
        original_fmt = self._style._fmt

        # Apply colors if enabled
        if self.use_colors:
            level_color = self.COLORS.get(record.levelname, self.COLORS['RESET'])
            reset_color = self.COLORS['RESET']
            self._style._fmt = f"{level_color}%(asctime)s | %(levelname)-8s | %(name)-15s{reset_color} | %(message)s"

        # Format the record
        result = logging.Formatter.format(self, record)

        # Restore the original format
        self._style._fmt = original_fmt

        return result

class AppLogger:
    def __init__(self, name: str, level: int = logging.DEBUG) -> None:
        # Get or create logger
        self.logger = logging.getLogger(name)
        self.logger.setLevel(level)

        # Only set up handlers if this logger hasn't been configured yet
        if name not in _configured_loggers:
            self._setup_handlers()
            _configured_loggers[name] = True

        # Prevent propagation to root logger to avoid duplicate logs
        self.logger.propagate = False

    def _setup_handlers(self) -> None:
        # First, remove any existing handlers to prevent duplicates
        if self.logger.handlers:
            self.logger.handlers.clear()

        # Create console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.DEBUG)

        # Create custom formatter
        formatter = CustomFormatter()
        console_handler.setFormatter(formatter)

        # Add handler to the logger
        self.logger.addHandler(console_handler)

    def get_logger(self) -> logging.Logger:
        return self.logger