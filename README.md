
# Product Studio Services
APIs for product studio to interact with AAVA Core Platform.

## Product Studio Capabilities

# Installation Guidelines
This project uses UV for python project management and virtual environment handling.

## Getting Started

### Prerequisites

- Python 3.12 or higher
- UV package manager ([Install UV](https://docs.astral.sh/uv/getting-started/installation/))
- PostgreSQL (or your preferred database) [`brew install postgresql`]

### Installation

1. **Clone the repository:**
    ```bash
    git clone https://github.com/yourusername/fastapi-project-template.git
    cd fastapi-project-template
    ```

2. **Create and activate virtual environment with uv:**
    ```bash
    # Create virtual environment
    uv venv

    # Activate virtual environment
    source .venv/bin/activate  # On Windows use `.venv\Scripts\activate`

    # Or use uv's automatic activation
    uv run python --version
    ```

3. **Install dependencies:**
    ```bash
    # Install all dependencies (including dev)
    uv sync

    # Or install only production dependencies
    uv sync --no-dev
    ```

4. **Install pre-commit hooks:**
    ```bash
    uv run pre-commit install
    ```

5. **Configure environment variables:**
    Create a `.env` file in the project root with your configuration.

6. **Run database migrations:**
    ```bash
    uv run alembic upgrade head
    ```

7. **Start the FastAPI application:**
    ```bash
    # Using uv (recommended)
    uv run python main.py

    # Or using uvicorn directly
    uv run uvicorn src.main:app --reload

    # Quick start
    uv run main.py
    ```

## Development Commands

```bash
# Run tests
uv run pytest

# Run pre-commit hooks manually
uv run pre-commit run --all-files

# Add new dependency
uv add package-name

# Add dev dependency
uv add --dev package-name

# Update dependencies
uv sync

# Run security audit
uv run pip-audit

# Format code
uv run ruff format

# Lint code
uv run ruff check --fix
```

## Project Structure

The project is organized into a modular structure to promote clean code and maintainability. Below is a brief overview of the directory structure:

### `src/`
- **`exceptions/`**: Custom exception handling classes.
- **`helpers/`**: Utility functions and business logic methods.
- **`migrations/`**: Database migrations (use Alembic for managing migrations).
- **`models/`**: ORM and database models.
- **`routers/`**: API route definitions.
- **`schemas/`**: Pydantic schemas for data validation and serialization.
- **`security/`**: Authentication and authorization logic.
- **`settings/`**: Configuration settings.
- **`utils/`**: Various utility functions.

### `test/`
- Contains test cases reflecting the structure of the `src` directory. Tests are organized to match the source code structure, ensuring comprehensive coverage and easy navigation.

## Running Tests

To run the tests, use the following command in the root directory of the project:

```bash
pytest
```

## Usage

- **API Documentation**: Access the interactive API documentation at `http://127.0.0.1:8000/docs` or `http://127.0.0.1:8000/redoc`.
- **Configuration**: Adjust settings in the `.env` file for different environments.

## Contributing

Contributions are welcome! Please fork the repository and submit a pull request with your changes.

## License

This project is licensed under the MIT License. See the [LICENSE](LICENSE) file for details.
